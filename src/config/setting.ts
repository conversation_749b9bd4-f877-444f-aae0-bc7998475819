import { reactive } from 'vue'

export const defaultSettings: App.AppSettings = {
  "theme": "light",
  "themeColor": "#165DFF",
  "tab": true,
  "tabMode": "card-gutter",
  "animate": true,
  "animateMode": "fade",
  "menuCollapse": false,
  "menuAccordion": true,
  "menuDark": true,
  "copyrightDisplay": true,
  "layout": "left",
  "isOpenWatermark": true,
  "enableColorWeaknessMode": false,
  "enableMourningMode": false
}

// 根据环境返回配置
export const getSettings = (): App.AppSettings => {
  return defaultSettings
}

const settingConfig = reactive({ ...getSettings() }) as App.AppSettings
// 如果没配置，就默认深色菜单
if (settingConfig.menuDark === undefined) {
  settingConfig.menuDark = true
}