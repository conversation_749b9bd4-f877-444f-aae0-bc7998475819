import { createApp } from 'vue'
import ArcoVue, { <PERSON>, Drawer, Modal } from '@arco-design/web-vue'
import '@/styles/arco-ui/index.less'
import { animateNumberDirective } from '@/utils/digita'
// import '@arco-themes/vue-gi-demo/index.less'
// import '@arco-design/web-vue/dist/arco.css'

// 额外引入 Arco Design Icon图标库
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
import App from './App.vue'
import router from './router'
import ECharts from 'vue-echarts'
import { use } from 'echarts/core'

import {
    CanvasRenderer
} from 'echarts/renderers'
import {
    LineChart,
    BarChart,
    PieChart
} from 'echarts/charts'
import {
    GridComponent,
    TooltipComponent,
    TitleComponent,
    LegendComponent
} from 'echarts/components'
// 注册组件
use([
    CanvasRender<PERSON>,
    LineChart,
    BarChart,
    PieChart,
    GridComponent,
    TooltipComponent,
    TitleComponent,
    LegendComponent
])

// 使用动画库
import 'animate.css/animate.min.css'

// 自定义过渡动画
import '@/styles/css/transition.css'

// 导入全局scss主文件
import '@/styles/index.scss'

// 支持SVG
import 'virtual:svg-icons-register'

// 自定义指令
import directives from './directives'

// 状态管理
import pinia from '@/stores'

// 对特定组件进行默认配置
Card.props.bordered = false

const app = createApp(App)
app.component('v-chart', ECharts)
Modal._context = app._context
Drawer._context = app._context

app.directive('animate-number', animateNumberDirective)
app.use(router)
app.use(pinia)
app.use(ArcoVue)
app.use(ArcoVueIcon)
app.use(directives)



app.mount('#app')
