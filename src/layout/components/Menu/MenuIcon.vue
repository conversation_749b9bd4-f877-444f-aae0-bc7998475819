<template>
  <template v-if="isImage(props.icon)">
    <img :src="props.icon" class="menu-icon" />
  </template>
  <template v-else>
    <GiSvgIcon :name="props.icon" :size="18" />
  </template>
</template>

<script setup lang="ts">
interface Props {
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {})

function isImage(icon?: string): boolean {
  if (!icon) return false
  return /\.(png|jpe?g|svg)$/i.test(icon)
}
</script>

<style scoped lang="scss">
.menu-icon {
  width: 18px;
  height: 18px;
  object-fit: contain;
}
</style>
