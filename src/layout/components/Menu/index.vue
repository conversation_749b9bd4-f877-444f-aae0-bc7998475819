<template>
  <a-menu
    :mode="mode"
    :selected-keys="activeMenu"
    :auto-open-selected="autoOpenSelected"
    :accordion="appStore.menuAccordion"
    :breakpoint="appStore.layout === 'mix' ? 'xl' : undefined"
    :trigger-props="{ animationName: 'slide-dynamic-origin' }"
    :collapsed="!isDesktop ? false : appStore.menuCollapse"
    :style="menuStyle"
    @menu-item-click="onMenuItemClick"
    @collapse="onCollapse"
  >
    <MenuItem   v-for="(item, index) in sidebarRoutes" :key="item.path + index" :item="item"></MenuItem>
  </a-menu>
</template>

<script setup lang="ts">
import type { RouteRecordRaw } from 'vue-router'
import type { CSSProperties } from 'vue'
import MenuItem from './MenuItem.vue'
import { useAppStore, useRouteStore } from '@/stores'
import { isExternal } from '@/utils/validate'
import { useDevice } from '@/hooks'

defineOptions({ name: 'AppMenu' })
const props = withDefaults(defineProps<Props>(), {})

const emit = defineEmits<{
  (e: 'menu-item-click-after'): void
}>()
interface Props {
  menus?: RouteRecordRaw[]
  menuStyle?: CSSProperties
}

const { isDesktop } = useDevice()
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const routeStore = useRouteStore()
const sidebarRoutes = computed(() => (props.menus ? props.menus : routeStore.routes))

// 菜单垂直模式/水平模式
const mode = computed(() => {
  if (!['left', 'mix'].includes(appStore.layout)) {
    return 'horizontal'
  } else {
    return 'vertical'
  }
})

// 是否默认展开选中的菜单
const autoOpenSelected = computed(() => {
  return ['left', 'mix'].includes(appStore.layout)
})

// 当前页面激活菜单路径，先从路由里面找
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta?.activeMenu) {
    return [meta.activeMenu]
  }
  return [path]
})

// 菜单项点击事件
const onMenuItemClick = (key: string) => {
  if (isExternal(key)) {
    window.open(key)
    return
  }
  router.push({ path: key })
  emit('menu-item-click-after')
}

// 折叠状态改变时触发
const onCollapse = (collapsed: boolean) => {
  if (appStore.layout === 'mix') {
    appStore.menuCollapse = collapsed
  }
}
</script>

<style scoped lang="scss">
// :deep(.arco-menu) {
//   background-color: #1e1e2d !important; /* 菜单整体背景色 */
// }

// :deep(.arco-menu-item) {
//   color: #ffffff !important; /* 默认文字颜色 */
// }

// :deep(.arco-menu-item:hover) {
//   background-color: #2a2a3d !important; /* hover 背景色 */
//   color: #54e49c !important; /* hover 字体颜色 */
// }

// :deep(.arco-menu-item.arco-menu-selected) {
//   background-color: #33334d !important; /* 选中背景色 */
//   color: #54e49c !important; /* 选中文字颜色 */
// }

// :deep(.arco-sub-menu-title) {
//   color: #ffffff !important; /* 子菜单标题颜色 */
// }

// :deep(.arco-sub-menu-title:hover) {
//   background-color: #2a2a3d !important;
//   color: #54e49c !important;
// }
</style>
