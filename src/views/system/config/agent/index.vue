<template>
    <a-spin :loading="loading">
        <div style=" padding: 10px;max-width: 400px; margin-left: 0;">
            <template v-for="item in configList" :key="item.code">
                <div style="padding: 10px;margin-bottom: 16px;display: flex;flex-direction: row;align-items: center;">
                    <div style="margin: 0 20px 0 0;width: 30%;">{{ item.name }}</div>
                    <div style="width: 70%;">
                        <a-input  v-model="formData[item.code]"  :disabled="!isEditMode" />
                        <div style="color: #888; font-size: 12px; margin-top: 10px;">{{ item.description }}</div>
                    </div>
                </div>
            </template>
            <div style="text-align: start;margin-left: 11px;" >
                <a-button v-if="!isEditMode" style="margin-right: 20px;" type="primary" @click="isEditMode = true">
                    <template #icon>
                        <icon-edit />
                    </template>
                    修改</a-button>
                <a-button v-else type="primary" style="margin-right: 20px;" :loading="saving"   @click="handleSave" >
                    保存
                    <template #icon>
                        <icon-save />
                    </template>
                </a-button>
                <a-button v-if="isEditMode"   :loading="saving" @click="isEditMode = false"
                    type="outline">
                    <template #icon>
                        <icon-undo />
                    </template>

                    取消
                </a-button>
            </div>
        </div>
    </a-spin>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { getAgentConfigApi,  } from '@/apis/system/agent';
import { updateOption } from '@/apis/system'

defineOptions({ name: 'AgentConfig' });

const loading = ref(false);
const saving = ref(false);
const configList =ref([]);
const config = ref([])
const formData = ref<Record<string, any>>({});
const isEditMode = ref(false);

const fetchConfig = async () => {
  loading.value = true;
  try {
    const res = await getAgentConfigApi();
    if (res && res.data) {
      configList.value = res.data;
      const data: Record<string, any> = {};
      res.data.forEach((item: any) => {
        data[item.code] = item.value;
      });
      formData.value = data;
    }
  } finally {
    loading.value = false;
  }
};

const handleSave = async () => {
  saving.value = true;
  try {
    const payload = Object.keys(formData.value).map(code => {
      const existing = configList.value.find(item => item.code === code);
      return {
        id: existing ? existing.id : undefined,
        code,
        value: formData.value[code],
      };
    });
    await updateOption(payload);
    Message.success('保存成功');
    isEditMode.value = false;
    await fetchConfig();
  } catch (e) {
    Message.error('保存失败');
  } finally {
    saving.value = false;
  }
};

onMounted(fetchConfig);
</script>
<style>
.btnStyle{
    display: flex;
    flex-direction: row;
    width: 400px;
    border: 1px solid black;
    text-align: start;
}
</style>
