<template>
  <div class="gi_page">
    <a-card>
      <div>

        <!-- <DataCard
          :theme="theme"
          primaryColor="#1e40af"
          upTrendColor="#22c55e"
          downTrendColor="#ef4444"
          highlightColor="#f59e42"
        /> -->
        <tempvView/>
      </div>

      <div style="margin-top: 50px;">
        <lineData />
      </div>
    </a-card>

  </div>
</template>
<script lang="ts" setup>
import DataCard from './conponents/dataCard.vue';
import lineData from './conponents/line.vue';
import tempvView from './conponents/tempvView.vue';
import { useAppStore } from '@/stores/useAppStore';
import { ref, computed } from 'vue';
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
</script>
<style>
</style>