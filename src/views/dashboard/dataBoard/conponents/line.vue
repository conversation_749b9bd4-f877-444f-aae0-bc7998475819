<template>
  <div class=" baidu-trend ">
    <h2>消耗数据</h2>
    <div class="chart-container">
      <div ref="trendChart" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { useAppStore } from '@/stores/useAppStore'
const appStore = useAppStore()
// 读取主题状态
const theme = computed(() => appStore.theme)
defineOptions({ name: 'lineData' })
const trendChart = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null


function getMockData(themeValue: string) {
  return {
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
    },
    yAxis: {
      type: 'value'
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '3%',  // 右边留点空，避免图例覆盖
    },
    series: [
      {
        name: '全部产品线',
        data: [65, 59, 80, 81, 56, 55],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: 'rgb(75, 192, 192)'
        },
      },
      {
        name: '凤巢',
        data: [45, 50, 60, 70, 65, 60],
        type: 'line',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#7B4AF4' },
            { offset: 1, color: '#F23365' }
          ]),
          borderRadius: [6, 6, 0, 0]
        }
      },
      {
        name: '原生',
        data: [20, 25, 30, 35, 40, 45],
        type: 'line',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#7B4AF4' },
            { offset: 1, color: '#F23365' }
          ]),
          borderRadius: [6, 6, 0, 0]
        }
      },
      {
        name: '品牌',
        data: [100, 68, 96, 125, 30, 80],
        type: 'bar',
        barWidth: 30,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#7B4AF4' },
            { offset: 1, color: '#F23365' }
          ]),
          borderRadius: [6, 6, 0, 0]
        }
      }
    ],
    legend: {
      data: ['全部产品线', '凤巢', '原生', '品牌'],
      top: 'bottom',
      left: 'center',
      textStyle: {
        color: themeValue === 'dark' ? '#fff' : '#000'
      }
    },
    // dataZoom: [
    //   { type: 'slider', start: 0, end: 100 },
    //   { type: 'inside', start: 0, end: 100 }
    // ]
  }
}
function initChart() {
  if (trendChart.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(trendChart.value, theme.value === 'dark' ? '' : 'light')
    const option = getMockData(theme.value)
    chartInstance.setOption(option)
  }
}
onMounted(() => {
  initChart()
})
watch(theme, () => {
  initChart()
})
</script>

<style scoped lang="scss">
.baidu-trend {
//   background-color: #fff;
    border-radius: 8px;

  h2 {
    margin-bottom: 16px;
    font-weight: 600;
    // color: #333;
  }

  .chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    // background-color: #000;
  }
}
</style>
