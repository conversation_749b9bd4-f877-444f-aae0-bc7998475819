<template>
  <div
    class="dashboard-container"
    :class="theme"
  >
    <!-- 页面头部 -->
    <header class="dashboard-header">
      <div class="header-content">
        <h1>区域运营数据看板</h1>
        <p class="update-time">更新时间: 2023年08月26日</p>
      </div>

      <!-- 区域切换器 -->
      <div class="region-switcher">
        <a-radio-group
          v-model="activeRegion"
          type="radio"
          size="large"
          @change="handleRegionChange"
        >
          <a-radio
            :value="item.value"
            v-for="item in source"
            :key="item.value"
          >{{ item.area }}</a-radio>
        </a-radio-group>
      </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 区域总览卡片 -->
      <section
        class="overview-card"
        :class="activeRegion"
      >
        <div class="overview-header">
          <h2>{{ activeRegion === 'north' ? '华北区域' : '华东区域' }} - 08月总览</h2>
          <a-tag :color="activeRegion === 'north' ? '#165DFF' : '#40B429'">本月数据</a-tag>
        </div>

        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-label">总任务目标</div>
            <div class="stat-value">{{ currentData.totalTask }}</div>
            <div class="progress-bar">
              <a-progress
                :percent="currentData.progressRate"
                :stroke-width="8"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-label">总消耗</div>
            <div class="stat-value highlight">{{ currentData.totalCost }}</div>
          </div>
        </div>
      </section>

      <!-- 部门数据卡片 -->
      <section class="departments-grid">
        <!-- 营销一部 -->
        <div
          class="dept-card"
          :class="activeRegion"
        >
          <div class="dept-header">
            <h3>营销策略一部</h3>
          </div>

          <div class="dept-stats">
            <div class="dept-stat-item">
              <span class="stat-label">部门任务</span>
              <span class="stat-value">{{ currentData.dept1.task }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">当月消耗</span>
              <span class="stat-value">{{ currentData.dept1.monthCost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">昨日消耗</span>
              <span class="stat-value">{{ currentData.dept1.dayCost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">任务进度</span>
              <a-progress
                :percent="currentData.dept1.progress"
                :stroke-width="8"
                :style="{width: '150px'}"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
              <!-- <span class="stat-value progress">{{ currentData.dept1.progress }}%</span> -->
            </div>
          </div>
        </div>

        <!-- 营销二部 -->
        <div
          class="dept-card"
          :class="activeRegion"
        >
          <div class="dept-header">
            <h3>营销策略二部</h3>

          </div>

          <div class="dept-stats">
            <div class="dept-stat-item">
              <span class="stat-label">部门任务</span>
              <span class="stat-value">{{ currentData.dept2.task }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">当月消耗</span>
              <span class="stat-value">{{ currentData.dept2.monthUgcost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">昨日消耗</span>
              <span class="stat-value">{{ currentData.dept2.dayUgcost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">任务进度</span>
              <a-progress
                :percent="currentData.dept2.progress"
                :stroke-width="8"
                :style="{width: '150px'}"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
              <!-- <span class="stat-value progress">{{ currentData.dept2.progress }}%</span> -->
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import { useUserStore } from '@/stores/modules/user'

const userStore = useUserStore()
const source = ref();
onMounted(async () => {
  await userStore.getInfo()
  const deptPath = userStore.userInfo.deptPathName || ''

  if (deptPath.includes('华东')) {
    activeRegion.value = 'east'
    source.value = [{ area: '华东区域', value: 'east' }]
  } else if (deptPath.includes('华北')) {
    activeRegion.value = 'north'
    source.value = [{ area: '华北区域', value: 'north' }]
  } else {
    // 如果没有华东/华北，则显示全部区域
    source.value = [
      { area: '华东区域', value: 'east' },
      { area: '华北区域', value: 'north' }
    ]
  }
})

// 主题切换
const appStore = useAppStore();
const theme = computed(() => appStore.theme);

// 区域切换
const activeRegion = ref('east'); // 'north' 或 'east'
// 数据结构定义
interface RegionData {
  totalTask: string;
  totalCost: string;
  progressRate: number;
  dept1: {
    task: string;
    monthCost: string;
    dayCost: string;
    progress: number;
  };
  dept2: {
    task: string;
    monthUgcost: string;
    dayUgcost: string;
    progress: number;
  };
}

// 华北和华南数据
const regionData = {
  north: {
    totalTask: '¥19,270,000.00',
    totalCost: '¥9,092,528.01',
    progressRate: 0.472,
    dept1: {
      task: '¥8,370,000.00',
      monthCost: '¥2,882,538.38',
      dayCost: '¥72,140.92',
      progress: 0.344,
    },
    dept2: {
      task: '¥10,900,000.00',
      monthUgcost: '¥6,209,989.63',
      dayUgcost: '¥355,817.80',
      progress: 0.56,
    },
  },
  east: {
    totalTask: '¥17,850,000.00',
    totalCost: '¥9,335,550.00',
    progressRate: 0.523,
    dept1: {
      task: '¥7,500,000.00',
      monthCost: '¥4,125,360.25',
      dayCost: '¥98,720.50',
      progress: 0.55,
    },
    dept2: {
      task: '¥10,350,000.00',
      monthUgcost: '¥5,480,225.75',
      dayUgcost: '¥312,450.30',
      progress: 0.53,
    },
  },
};
function handleRegionChange () {

  console.log('activeRegion.value:', activeRegion.value);
}
// 当前选中区域的数据
const currentData = computed<RegionData>(() => {
  return regionData[activeRegion.value as keyof typeof regionData];
});
</script>

<style scoped>
/* 基础样式 */
.dashboard-container {
  /* min-height: 100vh; */
}

/* 亮色主题 */
.light {
  /* background-color: #f5f7fa; */
}

/* 暗色主题 */
.dark {
  background-color: #1d1e23;
  color: #f2f3f5;
}

/* 头部样式 */
.dashboard-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.light .header-content h1 {
  color: #1d2129;
}

.dark .header-content h1 {
  color: #ffffff;
}

.update-time {
  margin-top: 10px;
  font-size: 13px;
  transition: color 0.3s ease;
}

.light .update-time {
  color: #86909c;
}

.dark .update-time {
  color: #c9cdd4;
}

.region-switcher {
  /* width: 100%; */
}

/* 主内容区 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 总览卡片 */
.overview-card {
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-top: 3px solid;
}

.light .overview-card {
  background-color: #ffffff;
}

.dark .overview-card {
  background-color: #27282f;
}

.overview-card.north {
  border-top-color: #165dff;
}

.overview-card.east {
  border-top-color: #40b429;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overview-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.light .overview-header h2 {
  color: #1d2129;
}

.dark .overview-header h2 {
  color: #ffffff;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  padding: 14px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.light .stat-item {
  background-color: #f9fafb;
}

.dark .stat-item {
  background-color: #303138;
}

.stat-label {
  font-size: 13px;
  margin-bottom: 8px;
  margin-right: 10px;

  transition: color 0.3s ease;
}

.light .stat-label {
  color: #86909c;
}

.dark .stat-label {
  color: #c9cdd4;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.light .stat-value {
  color: #1d2129;
}

.dark .stat-value {
  color: #f2f3f5;
}

.stat-value.highlight {
  font-weight: 700;
}

.overview-card.north .stat-value.highlight {
  color: #165dff;
}

.overview-card.east .stat-value.highlight {
  color: #40b429;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-text {
  font-size: 12px;
  text-align: right;
  transition: color 0.3s ease;
}

.light .progress-text {
  color: #86909c;
}

.dark .progress-text {
  color: #c9cdd4;
}

.growth-indicator {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #00b42a;
}

.trend-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 部门卡片网格 */
.departments-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 部门卡片 */
.dept-card {
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.light .dept-card {
  background-color: #ffffff;
}

.dark .dept-card {
  background-color: #27282f;
}

.dept-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dept-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.light .dept-header h3 {
  color: #1d2129;
}

.dark .dept-header h3 {
  color: #ffffff;
}

.dept-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.dept-stat-item {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.light .dept-stat-item {
  background-color: #f9fafb;
}

.dark .dept-stat-item {
  background-color: #303138;
}

.dept-stat-item .stat-value {
  font-size: 15px;
  margin-bottom: 0;
}

.dept-stat-item .stat-value.progress {
  font-weight: 700;
}

.dept-card.north .stat-value.progress {
  color: #165dff;
}

.dept-card.east .stat-value.progress {
  color: #40b429;
}

/* 趋势分析卡片 */

/* 媒体查询 - 平板及以上设备 */
@media (min-width: 768px) {
  .dashboard-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .overview-stats {
    flex-direction: row;
  }

  .stat-item {
    flex: 1;
  }

  .departments-grid {
    flex-direction: row;
  }

  .dept-card {
    flex: 1;
  }

  .chart-container {
    height: 250px;
  }
}

/* 媒体查询 - 桌面设备 */
@media (min-width: 1024px) {
  .dashboard-container {
    /* max-width: 1200px; */
    margin: 0 auto;
    /* padding: 32px; */
  }

  .overview-card,
  .dept-card,
  .trend-card {
    padding: 20px;
  }

  .overview-header h2 {
    font-size: 18px;
  }

  .stat-value {
    font-size: 22px;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
