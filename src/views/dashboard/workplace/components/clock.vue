<template>
  <!-- 时间显示容器 - 红色框区域 -->
  <div class="time-display-container">
    <div style="display: flex;flex-direction: row;align-items: center;justify-content: center;">
        <!-- 日期显示 -->
        <div class="date-text">{{ formattedDate }}</div>
        <!-- 星期显示 -->
        <div class="weekday-text">{{ weekday }}</div>
    </div>

    <!-- 时间显示（主内容） -->
    <div class="time-text">{{ formattedTime }}</div>

  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
defineOptions({ name: 'Clock' })
// 存储当前时间
const currentTime = ref<Date>(new Date());

// 格式化日期显示 (例如: 2023年10月15日)
const formattedDate = computed(() => {
  const year = currentTime.value.getFullYear();
  const month = currentTime.value.getMonth() + 1;
  const day = currentTime.value.getDate();
  return `${year}年${month}月${day}日`;
});

// 格式化时间显示 (例如: 14:35:22)
const formattedTime = computed(() => {
  const hours = String(currentTime.value.getHours()).padStart(2, '0');
  const minutes = String(currentTime.value.getMinutes()).padStart(2, '0');
  const seconds = String(currentTime.value.getSeconds()).padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
});

// 星期几显示
const weekday = computed(() => {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekDays[currentTime.value.getDay()];
});

// 定时器ID
let timer: number | null = null;

// 组件挂载时启动定时器
onMounted(() => {
  // 每秒更新一次时间
  timer = window.setInterval(() => {
    currentTime.value = new Date();
  }, 1000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.time-display-container {
  /* 红色框样式 - 根据您的实际需求调整 */
  padding: 12px 16px;
  /* background-color: #fff; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  min-width: 200px;
  text-align: center;
}

.date-text {
  font-size: 12px;
  /* margin-bottom: 4px; */
}

.time-text {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin: 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.time-text::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E") no-repeat center;
}

.weekday-text {
  font-size: 12px;
}
</style>
