<template>
  <a-card class="card welcome-card" :bordered="true" :style="welcomeCardStyle">
    <a-row align="center" wrap :gutter="[{ xs: 0, sm: 14, md: 14, lg: 14, xl: 14, xxl: 14 }, 16]" class="content">
      <div  style="width: 100%;display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
        <!-- <Avatar :src="userStore.avatar" :name="userStore.nickname" :size="68" /> -->
        <div class="welcome">
          <p class="hello">{{ goodTimeText() }}！{{ userStore.nickname }}</p>
          <p>能观其始，析其变，则知其终</p>
        </div>
        <div>
          <Clock />
        </div>
      </div>
    </a-row>
  </a-card>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores'
import { goodTimeText } from '@/utils'
import { useAppStore } from '@/stores/useAppStore'
import Clock from './clock.vue'
const appStore = useAppStore()
// 读取主题状态
import { computed } from 'vue'
const theme = computed(() => appStore.theme)

const welcomeCardStyle = computed(() => {
  return {
    background: theme.value === 'dark'
      ? 'linear-gradient(to bottom, #888F96 10%, #60656A 20%, #47494E 40%, #000000 100%)'
      : 'linear-gradient(to bottom, #F4F9FF 10%, #F6FBFF 20%, #FBFDFF 40%, #ffffff 100%)'
  }
})

const userStore = useUserStore()
</script>

<style scoped lang="scss">
:deep(.arco-statistic-title) {
  margin-bottom: 0;
}

.card {
  .content {
    padding: 20px 8px;
    .welcome {
      // margin: 8px 0;
      color: $color-text-3;
      .hello {
        font-size: 1.25rem;
        color: $color-text-1;
        margin-bottom: 10px;
      }
    }
  }
}
.welcome-card{
  width: 100%;
  margin-bottom: 20px;
  border-radius: 8px;
  /* background is now dynamic via :style */
}
</style>
