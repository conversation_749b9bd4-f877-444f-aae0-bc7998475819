<template>
  <div class="gi_page">
    <a-tabs
      size="large"
      default-active-key="1"
      @change="change"
    >
      <a-tab-pane
        key="1"
        title="百度看板"
      >
        <BaiduBoard/>
      </a-tab-pane>
      <a-tab-pane
        key="2"
        title="业绩报表"
      >
        <TableCassify/>
      </a-tab-pane>
      <a-tab-pane
        key="3"
        title="框架报表"
      >
        <FrameTable/>
      </a-tab-pane>
      <a-tab-pane
        key="4"
        title="账户报表"
      >
        <AccountTable/>
      </a-tab-pane>
      <!-- <a-tab-pane
        key="5"
        title="自定义报表配置"
      >
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>
<script lang="ts" setup>
import  BaiduBoard  from './components/baiduBorad.vue';

import TableCassify  from './components/tablecCassify.vue';

import FrameTable  from './components/frameTable.vue';
import AccountTable  from './components/accountTable.vue';
defineOptions({ name: 'Baidu' });
function change(activeKey: string | number) {
  console.log(activeKey);
}
</script>
<style lang="scss">
@use '@/styles/var.scss' as *;

</style>
