<template>
  <div class="new-customer-management" :class="theme">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">新客管理</h1>
    </div>

    <!-- 顶部导航标签 -->
    <a-tabs
      v-model:activeKey="activeTopTab"
      size="large"
      class="top-tabs"
      :destroy-on-hide="false"
      @change="handleTopTabChange"
    >
      <a-tab-pane key="1" title="新客开发" >
        <NewDevelop ref="developRef"/>
      </a-tab-pane>
      <a-tab-pane key="2" title="新客激励" >
        <Motivate/>
      </a-tab-pane>
      <a-tab-pane key="3" title="新客体量" >
        <Volume/>
      </a-tab-pane>
      <a-tab-pane key="4" title="消耗top10" >
        <DepleteTop/>
      </a-tab-pane>
      <a-tab-pane key="5" title="重点框架监控" >
        <ImpMonitor/>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'NewClient' });
import { ref, computed, nextTick } from 'vue';
import type { ComputedRef } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import NewDevelop from './newClientmanage/newDevelop.vue';
import DepleteTop from './newClientmanage/depleteTop.vue';
import ImpMonitor from './newClientmanage/impmonitor.vue';
import Motivate from './newClientmanage/motivate.vue';
import Volume from './newClientmanage/volume.vue';
const appStore = useAppStore();
const theme: ComputedRef<string> = computed(() => appStore.theme);
// 顶部标签状态
const activeTopTab = ref<string>('1')
const developRef = ref<InstanceType<typeof NewDevelop> | null>(null);

const handleTopTabChange = async (key: string) => {
  activeTopTab.value = key;
  if (key === '1') {
    await nextTick();
    developRef.value?.refreshTable();
  }
};

</script>

<style scoped>
.new-customer-management {
  background-color: var(--color-bg-1, #f5f7fa);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}
.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-1, #1d2129);
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.top-tabs {
  border-radius: 4px;
}

</style>
