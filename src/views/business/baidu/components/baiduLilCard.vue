<template>
  <div class="data-view-item" :class="theme === 'dark' ? 'isDark' : ''" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave">
    <div class="header-top">
      <div class="title-container">
        <img class="title-icon" :src="icon" alt="任务图标"/>
        <span class="title-text">{{ title }}</span>
      </div>
      <a-popover v-if="showPopover" :trigger="['hover', 'focus']">
        <icon-question-circle size="large" class="info-icon"/>
        <template #content>
          <p v-html="popoverContent" class="popover-content" :class="theme === 'dark' ? 'isDark' : ''"></p>
        </template>
      </a-popover>
    </div>
    <div class="value-container">
      <span class="value-text">{{ value }}</span>
    </div>
    <div class="hover-effect" :style="{ transform: isHovered ? 'scaleX(1)' : 'scaleX(0)' }"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useAppStore } from '@/stores/useAppStore';

defineOptions({ name: 'BaiduLilCard' });

const appStore = useAppStore();
const isHovered = ref(false);

// 读取主题状态
const theme = computed(() => appStore.theme);

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  popoverContent: {
    type: String,
    default: ''
  },
  showPopover: {
    type: Boolean,
    default: false
  },
  value: {
    type: String,
    default: ''
  }
});

const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};
</script>

<style lang="scss">
@use '@/styles/var.scss' as *;

.data-view-item {
  position: relative;
  height: 88px;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #ffffff;
  border: 1px solid #eee;
  overflow: hidden;

  // 亮色主题阴影
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), 0 3px 6px rgba(0, 0, 0, 0.05);
  }

  // 暗色主题样式
  &.isDark {
    background-color: #353535;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #404040;
    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2);
    }
  }
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  transition: transform 0.3s ease;

  .data-view-item:hover & {
    transform: scale(1.1);
  }
}

.title-text {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  transition: color 0.3s ease;

  .isDark & {
    color: #e0e0e0;
  }
}
.info-icon {
  color: #999;
  transition: color 0.3s ease;

  &:hover {
    color: #4096ff;
  }

  .isDark & {
    color: #777;

    &:hover {
      color: #69b1ff;
    }
  }
}

.value-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-top: 8px;
}

.value-text {
  font-size: 1.4em;
  font-weight: 700;
  color: #1a1a1a;
  transition: color 0.3s ease;
  line-height: 1.2;

  .isDark & {
    color: #fff;
  }
}

.hover-effect {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #4096ff;
  transform-origin: left center;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.popover-content {
  font-size: 12px;
  line-height: 1.5;
  color: #666;
   &.isDark {
    color: #fff;
  }
}
</style>
