<template>
  <div class="gi_class">
    <a-card>
      <div class="filter-box">
        <div class="filter-item">
          <p>框架执行截止日期：</p>
          <a-date-picker
            class="range-picker"
            v-model="formData.startDate"
            @change="onChange"
            :allow-clear="false"
          />
        </div>
        <div class="filter-item">
          <p>广告主：</p>
          <a-input
            class="range-picker"
            v-model="formData.custName"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            placeholder="请输入广告主"
            allow-clear
          ></a-input>
        </div>
      </div>

        <a-button
          type="primary"
          style="margin-top: 20px;"
          @click="exportData"
          :loading="btnLoading"
          size="large"
        >导出</a-button>
      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
      <template #depositFirstAmt="{ record }">
        {{ formatNumber(record.depositFirstAmt) }}
      </template>
      <template #totalContractAmt="{ record }">
        {{ formatNumber(record.totalContractAmt) }}
      </template>
       <template #nativeContractAmt="{ record }">
        {{ formatNumber(record.nativeContractAmt) }}
      </template>
      <template #brandContractAmt="{ record }">
        {{ formatNumber(record.brandContractAmt) }}
      </template>
      <template #totalAmt="{ record }">
        {{ formatNumber(record.totalAmt) }}
      </template>
      <template #totalFinishAmt="{ record }">
        {{ formatNumber(record.totalFinishAmt) }}
      </template>
      <template #totalGapAmt="{ record }">
        {{ formatNumber(record.totalGapAmt) }}
      </template>
      <template #totalCompleteAmt="{ record }">
        {{ formatNumber(record.totalCompleteAmt) }}
      </template>
      <template #nativeFinishAmt="{ record }">
        {{ formatNumber(record.nativeFinishAmt) }}
      </template>
      <template #nativeGapAmt="{ record }">
        {{ formatNumber(record.nativeGapAmt) }}
      </template>
      <template #nativeRate="{ record }">
        {{ formatNumber(record.nativeRate) }}
      </template>
      <template #nativeCompleteAmt="{ record }">
        {{ formatNumber(record.nativeCompleteAmt) }}
      </template>
      <template #brandFinishAmt="{ record }">
        {{ formatNumber(record.brandFinishAmt) }}
      </template>
      <template #brandGapAmt="{ record }">
        {{ formatNumber(record.brandGapAmt) }}
      </template>
      </GiTable>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import { onMounted } from 'vue';
import { exportListApi, frameExportListApi, getFrameworkListApi } from '@/apis/agent';
import { Message } from '@arco-design/web-vue';
import { formatNumber } from '@/utils/formatNumber';
defineOptions({ name: 'FrameTable' });
const advertiserName = ref('');
// 获取昨天日期并格式化为 yyyy.MM.dd
function formatDate(date: Date) {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}
const now = new Date();
const yesterday = new Date(now);
yesterday.setDate(now.getDate() - 1);
const formData = ref({
  startDate: formatDate(yesterday),
  custName: '',
});
const dataList = ref([]);
const columns = [
  { dataIndex: 'id', title: 'ID', width: 80, align: 'center' },
  { dataIndex: 'framePolicyNo', title: '框架编号', width: 120, align: 'center' },
  { dataIndex: 'frameExeEndDate', title: '框架执行截止时间', width: 160, align: 'center' },
  { dataIndex: 'frameCustTypeName', title: '客户关系类型', width: 130, align: 'center' },
  { dataIndex: 'customerAlbCode', title: '广告主ALB编码', width: 150, align: 'center' },
  { dataIndex: 'customerName', title: '广告主', width: 120, align: 'center' },
  { dataIndex: 'agentCode', title: '代理商ALB编码', width: 150, align: 'center' },
  { dataIndex: 'agentName', title: '代理商', width: 120, align: 'center' },
  { dataIndex: 'agentGroupName', title: '代理商集团', width: 130, align: 'center' },
  { dataIndex: 'salesManagerName', title: '销售经理', width: 110, align: 'center' },
  { dataIndex: 'salesAssistantName', title: '销售助理', width: 110, align: 'center' },
  { dataIndex: 'channelManagerName', title: '渠道经理', width: 110, align: 'center' },
  { dataIndex: 'policyYear', title: '框架政策年', width: 110, align: 'center' },
  { dataIndex: 'frameSignCategory', title: '新签/续签', width: 100, align: 'center' },
  { dataIndex: 'proxyOpFlag', title: '是否代运营', width: 110, align: 'center' },
  { dataIndex: 'isPlatformUser', title: '是否平台类客户', width: 130, align: 'center' },
  { dataIndex: 'custContractName', title: '客户关联广告主', width: 140, align: 'center' },
  { dataIndex: 'policyTradeName', title: '所签框架政策行业', width: 150, align: 'center' },
  { dataIndex: 'custCompanyContractName', title: '客户总分公司广告主', width: 160, align: 'center' },
  { dataIndex: 'checkPassTime', title: '签订时间', width: 120, align: 'center' },
  { dataIndex: 'frameStartDate', title: '框架开始时间', width: 130, align: 'center' },
  { dataIndex: 'frameEndDate', title: '框架结束时间', width: 130, align: 'center' },
  { dataIndex: 'frameCycle', title: '框架周期', width: 100, align: 'center' },
  { dataIndex: 'exeDays', title: '执行天数', width: 100, align: 'center' },
  { dataIndex: 'payMethodName', title: '框架保证金付款方式', width: 160, align: 'center' },
  { dataIndex: 'depositFirstRatio', title: '框架保证金比例（%）', width: 150, align: 'center' },
  { dataIndex: 'depositFirstAmt', title: '框架保证金金额', width: 140, align: 'center', slotName: 'depositFirstAmt' },
  { dataIndex: 'frameTradeName1st', title: '框架一级行业', width: 130, align: 'center' },
  { dataIndex: 'frameTradeName2nd', title: '框架二级行业', width: 130, align: 'center' },
  { dataIndex: 'totalContractAmt', title: '框架任务金额', width: 130, align: 'center', slotName: 'totalContractAmt' },
  { dataIndex: 'nativeContractAmt', title: '原生子框架任务金额', width: 160, align: 'center', slotName: 'nativeContractAmt' },
  { dataIndex: 'brandContractAmt', title: '品牌子框架任务金额', width: 160, align: 'center', slotName: 'brandContractAmt' },
  { dataIndex: 'frameSignMode', title: '框架签署模式', width: 130, align: 'center' },
  { dataIndex: 'baseFcDiscount', title: '搜索返点（%）', width: 120, align: 'center' },
  { dataIndex: 'nativeDiscount', title: '原生返点（%）', width: 120, align: 'center' },
  { dataIndex: 'brandDiscount', title: '品牌折扣（折）', width: 120, align: 'center' },
  { dataIndex: 'totalFinishAmt', title: '整体大框实际消费', width: 140, align: 'center', slotName: 'totalFinishAmt' },
  { dataIndex: 'totalGapAmt', title: '整体大框差距', width: 120, align: 'center', slotName: 'totalGapAmt' },
  { dataIndex: 'totalRate', title: '整体大框完成率（%）', width: 150, align: 'center' },
  { dataIndex: 'totalCompleteAmt', title: '整体大框非结算调整后实际完成金额', width: 200, align: 'center', slotName: 'totalCompleteAmt' },
  { dataIndex: 'totalCompleteRate', title: '整体大框非结算调整后完成率（%）', width: 190, align: 'center' },
  { dataIndex: 'nativeFinishAmt', title: '原生子框实际消费', width: 140, align: 'center', slotName: 'nativeFinishAmt' },
  { dataIndex: 'nativeGapAmt', title: '原生子框差距', width: 120, align: 'center', slotName: 'nativeGapAmt' },
  { dataIndex: 'nativeRate', title: '原生子框完成率（%）', width: 150, align: 'center', slotName: 'nativeRate' },
  { dataIndex: 'nativeCompleteAmt', title: '原生子框非结算调整后实际完成金额', width: 190, align: 'center', slotName: 'nativeCompleteAmt' },
  { dataIndex: 'nativeCompleteRate', title: '原生子框非结算调整后完成率（%）', width: 190, align: 'center'},
  { dataIndex: 'brandFinishAmt', title: '品牌子框实际消费', width: 140, align: 'center', slotName: 'brandFinishAmt' },
  { dataIndex: 'brandGapAmt', title: '品牌子框差距', width: 120, align: 'center', slotName: 'brandGapAmt' },
  { dataIndex: 'brandRate', title: '品牌子框完成率（%）', width: 150, align: 'center' }
];

const loading = ref(false);
const selectedKeys = ref([]); // 选中行的key
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
// --- Debounce and loading guard for getFrameworkList ---
let getFrameworkListLoading = false;
let getFrameworkListDebounceTimer: ReturnType<typeof setTimeout> | null = null;
const btnLoading = ref(false);
async function exportData() {
  // 校验是否有数据
  if (!dataList.value || dataList.value.length === 0) {
    Message.warning('暂无数据可导出');
    return;
  }

  btnLoading.value = true; // 开始 loading
  try {
    const res = await frameExportListApi(formData.value);
    // 检查res.data是否为Blob，且类型正确
    if (
      !(res.data instanceof Blob) ||
      !res.data.type.includes('spreadsheetml')
    ) {
      throw new Error('API返回的数据不是有效的Excel文件');
    }

    const blob = res.data;
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // 从响应头解析文件名（处理URL编码和默认值）
    let filename = '报表.xlsx';
    if (res.headers?.['content-disposition']) {
      const match = res.headers['content-disposition'].match(/filename=(.+)/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败详情:', e);
    Message.error(`导出失败: ${e.message}`);
  } finally {
    btnLoading.value = false;
  }
}

async function getFrameworkList() {
  // Prevent concurrent requests
  if (getFrameworkListLoading) return;
  getFrameworkListLoading = true;
  loading.value = true;
  try {
    const res = await getFrameworkListApi({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...formData.value,
    });
    dataList.value = res.data.records;
    pagination.total = res.data.total;
    console.log(res, '框架报表');
  } finally {
    getFrameworkListLoading = false;
    loading.value = false;
  }
}
// Debounced version for search input
function getFrameworkListDebounced() {
  if (getFrameworkListDebounceTimer)
    clearTimeout(getFrameworkListDebounceTimer);
  getFrameworkListDebounceTimer = setTimeout(() => {
    getFrameworkList();
  }, 300); // debounce delay (ms)
}
function onChange(date: any, dateString: any) {
  getFrameworkListDebounced();
  console.log('Selected Time: ', date);
  console.log('Formatted Selected Time: ', dateString);
}
function handleSearch() {
  getFrameworkListDebounced();
}
function handlePageChange(page) {
  pagination.currentPage = page;
  console.log('当前页码:', page); // 确认是否打印正确页码（如点击第2页应打印 2）
  getFrameworkList();
}
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  getFrameworkList();
}
onMounted(() => {
  getFrameworkList();
});
</script>

<style lang="scss" scoped>
@use '@/styles/var.scss' as *;
.filter-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
  width: 100%;
  align-items: start;
}

@media screen and (max-width: 768px) {
  .filter-box {
    grid-template-columns: 1fr;
  }
}

.filter-item {
  display: flex;
  align-items: start;
  flex-direction: column;
  gap: 8px;
  width: 100%;

  p {
    margin: 0;
    white-space: nowrap;
    flex-shrink: 0;
  }

  :is(.arco-picker, .arco-select, .arco-input-wrapper, .range-picker) {
    flex: 1;
    width: 100% !important;
  }

  @media screen and (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    p {
      margin-bottom: 6px;
    }
  }
}

</style>