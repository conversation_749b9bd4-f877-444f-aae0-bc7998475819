<template>
  <div class=" stats-container" >
    <!-- 标题区域 -->
    <!-- <div class="stats-header">
      <h1 class="stats-title">{{ title }}</h1>
      <p class="stats-subtitle">{{ subtitle }}</p>
    </div> -->
    <!-- 数据卡片容器 -->
    <div class="cards-wrapper"   >
      <!-- 循环渲染数据卡片 -->
      <div
      :class="theme === 'dark' ? 'isDark' : ''"
        class="stat-card"
        v-for="(item, index) in data"
        :key="index"
        :style="{ flex: '1 1 auto', maxWidth: cardWidthValue }"
      >
        <!-- 卡片标题和图标 -->
        <div class="card-header">
          <div class="card-icon">
            <img
              :src="item.iconImg"
              style="width: 30px; height: 30px; object-fit: contain;"
            />
          </div>
          <h2 class="card-title" >{{ item.title}}</h2>
        </div>

        <!-- 数据项 -->
        <div class="card-stats">
          <div class="stat-item" v-for="(stat, statIndex) in item.stats" :key="statIndex">
            <p class="stat-label">{{ stat.label }}</p>

            <!-- 进度条类型数据 -->
            <div v-if="stat.isProgress" class="progress-container">
              <span class="progress-value">{{ stat.value }}</span>
              <div class="progress-bar-outer">
                <div class="progress-bar-inner"
                     :style="{
                       width: stat.value,
                       backgroundColor: item.iconColor
                     }"></div>
              </div>
            </div>

            <!-- 普通文本类型数据 -->
            <p v-else class="stat-value">{{ stat.value }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <!-- <div class="stats-footer">
      <h3 class="footer-title">数据说明</h3>
      <p class="footer-info">{{ footerInfo }}</p>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
console.log(theme.value,'theme');
// 定义数据类型
interface StatItem {
  label: string;
  value: string;
  isProgress?: boolean;
}

interface CardData {
  title: string;
  icon: string;
  iconImg: string; // new property for image path
  iconBgColor: string;
  iconColor: string;
  stats: StatItem[];
}

// 定义组件属性
const props = defineProps({
//   title: {
//     type: String,
//     required: true
//   },
//   subtitle: {
//     type: String,
//     required: true
//   },
  data: {
    type: Array as () => CardData[],
    required: true
  },
//   footerInfo: {
//     type: String,
//     required: true
//   },
  cardWidth: {
    type: [String, Number],
    default: '280px'
  }
});

const cardWidthValue = computed(() => (typeof props.cardWidth === 'number' ? props.cardWidth + 'px' : props.cardWidth));
</script>

<style scoped lang="scss">
@use '@/styles/var.scss' as *;
/* 基础容器样式 */
.stats-container {
    max-width: 100%;
  /* padding: 20px;  */

}

/* 标题样式 */
.stats-header {
  /* margin-bottom: 30px; */
}

.stats-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.stats-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

/* 卡片容器 */
.cards-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

/* 单个卡片样式 */
.stat-card {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  // transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: white;
  &.isDark {
    background-color: #353535;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #404040;
  }
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  background-color: transparent;
}

.card-title {
  margin: 0 0 0 12px;
  font-size: 18px;
  color: #333;
   .isDark & {
    color: #ffffff;
  }
}

/* 统计数据项 */
.card-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 14px;
  color: #666;
  margin: 0 0 5px 0;
   .isDark & {
    color: #e0e0e0;
  }
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0;
   .isDark & {
    color: #fff;
  }
}

/* 进度条样式 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-value {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  min-width: 70px;
   .isDark & {
    color: #fff;
  }
}

.progress-bar-outer {
  flex: 1;
  height: 8px;
  background-color: #eee;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-inner {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* 底部信息 */
.stats-footer {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px 20px;
  background: white;
}

.footer-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 8px 0;
}

.footer-info {
  font-size: 14px;
  color: #666;
  margin: 0;
}

@media screen and (max-width: 768px) {
  .stat-card {
    flex: 0 0 calc(50% - 20px);
  }
}
@media screen and (max-width: 480px) {
  .stat-card {
    flex: 0 0 100%;
  }
}

</style>
