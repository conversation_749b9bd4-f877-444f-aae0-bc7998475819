<template>
  <div class="water-progress-container" :style="{ width: `${size}px`, height: `${size}px` }">
    <svg :width="size" :height="size" viewBox="0 0 100 100" class="progress-svg">
      <defs>
        <radialGradient id="light-gradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#8a2be2"/>
          <stop offset="100%" stop-color="#4b0082"/>
        </radialGradient>
        <radialGradient id="dark-gradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" stop-color="#6a5acd"/>
          <stop offset="100%" stop-color="#483d8b"/>
        </radialGradient>
        <linearGradient id="shimmer-gradient" x1="0" y1="0" x2="1" y2="0">
          <stop offset="0%" stop-color="rgba(255,255,255,0)" />
          <stop offset="50%" stop-color="rgba(255,255,255,0.4)" />
          <stop offset="100%" stop-color="rgba(255,255,255,0)" />
        </linearGradient>
        <linearGradient id="outer-light-gradient" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#8a2be2"/>
          <stop offset="100%" stop-color="#4b0082"/>
        </linearGradient>
        <linearGradient id="outer-dark-gradient" x1="0" y1="0" x2="0" y2="1">
          <stop offset="0%" stop-color="#6a5acd"/>
          <stop offset="100%" stop-color="#483d8b"/>
        </linearGradient>
        <radialGradient id="dynamic-water-gradient" cx="50%" cy="50%" r="50%">
          <stop
            v-for="(color, index) in gradientStops"
            :key="index"
            :offset="color.offset"
            :stop-color="color.color"
          />
        </radialGradient>
      </defs>
      <!-- Gray background circle for outer ring -->
      <circle
        cx="50"
        cy="50"
        :r="47"
        stroke="#ccc"
        :stroke-width="outerStrokeWidth"
        fill="none"
      />
      <!-- Outer progress ring -->
      <circle
        v-if="showOuter"
        cx="50"
        cy="50"
        :r="47"
        :stroke-width="outerStrokeWidth"
        fill="none"
        :stroke="typeof props.outerStrokeColor === 'string' && props.outerStrokeColor !== null && props.outerStrokeColor !== '' ? props.outerStrokeColor : (theme.value === 'dark' ? 'url(#outer-dark-gradient)' : 'url(#outer-light-gradient)')"
        stroke-linecap="round"
        :stroke-dasharray="2 * Math.PI * 47"
        :stroke-dashoffset="2 * Math.PI * 47 * (1 - progress / 100)"
        transform="rotate(-90 50 50)"
      />
      <!-- Mask for water inside circle -->
      <mask id="circle-mask">
        <rect width="100" height="100" fill="black" />
        <circle cx="50" cy="50" r="45" fill="white" />
      </mask>
      <!-- Clip path for water level -->
      <clipPath id="water-clip">
        <rect x="5" :y="waterLevel" width="90" :height="100 - waterLevel" />
      </clipPath>
      <!-- Water group with mask and clip -->
      <g mask="url(#circle-mask)" :clip-path="'url(#water-clip)'">
        <!-- Water background rectangle with dynamic gradient -->
        <rect
          x="5"
          y="0"
          width="90"
          height="100"
          fill="url(#dynamic-water-gradient)"
        />
        <!-- Particles -->
        <circle
          v-for="(particle, index) in particles"
          :key="index"
          :cx="particle.x"
          :cy="particle.y"
          :r="particle.size"
          :fill="particleColor"
          :opacity="particle.opacity"
        />
        <!-- Water shimmer -->
        <rect
          x="5"
          y="0"
          width="90"
          height="15"
          class="water-shimmer"
        >
          <animateTransform attributeName="transform" attributeType="XML" type="translate" from="-90 0" to="90 0" dur="6s" repeatCount="indefinite"/>
        </rect>
      </g>
    </svg>
    <!-- Progress text -->
    <div class="progress-text" :style="{ color: textColor }">
     <span
        class="progress-value"
        :style="{ fontSize: `${textSize}px`, lineHeight: 1 }"
      >{{ (progress % 1 === 0 ? progress.toFixed(0) : progress.toFixed(2)) }}%</span>
      <span
        v-if="innerText && innerText.length > 0"
        class="progress-custom-text"
        :style="{ fontSize: `${textSize * 0.6}px`, marginTop: '4px', lineHeight: 1.1 }"
      >{{ innerText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useAppStore } from '@/stores/useAppStore';

// Props
const props = defineProps({
  value: {
    // 进度值
    type: Number,
    default: 0,
    validator: (v: number) => v >= 0 && v <= 100,
  },
  size: {
    // 大小
    type: String,
    default: '200',
  },
  duration: {
    // 动画持续时间，单位秒
    type: Number,
    default:5,
  },
  gradientColors: {
    // 自定义水面渐变颜色数组
    type: [Array, String] as unknown as () => string[] | string,
    default: () => ['#58EDCE', '#58EDCE'],
  },
  outerStrokeWidth: {
    // 外圈进度条宽度
    type: Number,
    default: 3,
  },
  innerText: {
    // 自定义显示文字
    type: String,
    default: '',
  },
  particleColorProp: {
    //  气泡颜色，传字符串如 'rgba(88,237,206,0.7)'
    type: String,
    default: '',
  },
  outerStrokeColor: {
    // 外圈进度条颜色，支持颜色字符串或渐变
    type: [String, Array],
    default: null,
  },
  showOuter: {
    type: Boolean,
    default: true,
  },
});

const appStore = useAppStore();
const theme = computed(() => appStore.theme);

// Normalize gradientColors to array of strings
const gradientArray = computed<string[]>(() => {
  if (typeof props.gradientColors === 'string') {
    return [props.gradientColors];
  }
  return props.gradientColors;
});

// Generate gradient stops evenly spaced from 0 to 100%
const gradientStops = computed(() => {
  const colors = gradientArray.value;
  const len = colors.length;
  return colors.map((color, index) => {
    const offset = ((index) / (len - 1)) * 100 || 0;
    return {
      offset: `${offset}%`,
      color,
    };
  });
});

// Colors based on theme for particles and text
const particleColor = computed(() => {
  if (props.particleColorProp && props.particleColorProp.trim() !== '') {
    return props.particleColorProp;
  }
  return theme.value === 'dark' ? 'rgba(106,90,205,0.7)' : 'rgba(138,43,226,0.7)';
});
const textColor = computed(() => (theme.value === 'dark' ? '#e2e8f0' : '#334155'));
const textSize = 24;

// Internal progress for smooth animation
const progress = ref(0);
// Water level vertical position (0 top, 100 bottom)
const waterLevel = ref(100);

// Particle interface
interface Particle {
  x: number;
  y: number;
  size: number;
  opacity: number;
  speed: number;
}

const particles = ref<Particle[]>([]);

// Generate particles inside the water area
function generateParticles() {
  const newParticles: Particle[] = [];
  const count = 50;
  for (let i = 0; i < count; i++) {
    newParticles.push({
      x: Math.random() * 90 + 5,
      y: 100 - Math.random() * (100 - waterLevel.value),
      size: Math.random() * 1.5 + 0.5,
      opacity: Math.random() * 0.5 + 0.3,
      speed: Math.random() * 0.3 + 0.1,
    });
  }
  particles.value = newParticles;
}

// Animate particles upward and reset when leaving water area
function animate() {
  for (const p of particles.value) {
    p.y -= p.speed;
    if (p.y < waterLevel.value) {
      p.x = Math.random() * 90 + 5;
      p.y = 100;
      p.size = Math.random() * 1.5 + 0.5;
      p.opacity = Math.random() * 0.5 + 0.3;
      p.speed = Math.random() * 0.3 + 0.1;
    }
  }
  requestAnimationFrame(animate);
}

// Animate progress changes and water level
watch(
  () => props.value,
  (newVal) => {
    const start = progress.value;
    const end = newVal;
    const startTime = performance.now();

    function easeOutQuad(t: number) {
      return t * (2 - t);
    }

    function updateProgress(timestamp: number) {
      const elapsed = timestamp - startTime;
      const t = Math.min(elapsed / (props.duration * 1000), 1);
      const eased = easeOutQuad(t);
      progress.value = start + (end - start) * eased;
      waterLevel.value = 100 - progress.value;

      if (t < 1) {
        requestAnimationFrame(updateProgress);
      } else {
        // Regenerate particles when water level changes
        generateParticles();
      }
    }

    requestAnimationFrame(updateProgress);
  },
  { immediate: true }
);

onMounted(() => {
  generateParticles();
  animate();
});
</script>

<style scoped>
.water-progress-container {
  position: relative;
  user-select: none;
}
.progress-svg {
  display: block;
  margin: 0 auto;
}
.water-shimmer {
  fill: url(#shimmer-gradient);
}
.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: 700;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  white-space: normal;
  max-width: 90%;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}
.progress-value {
  font-weight: 700;
}
.progress-custom-text {
  font-weight: 400;
  opacity: 0.85;
}
</style>
