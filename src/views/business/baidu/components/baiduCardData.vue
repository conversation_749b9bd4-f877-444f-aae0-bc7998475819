<template>
  <div>
    <div  class="gi_box header_top">
      <h3>任务总览（元）</h3>
      <!-- :style="{ width: isMobile() ? '25%' : '100px' }" -->
      <div class="select-wrapper">
        <a-select
          v-model="sortType"
          placeholder="请选择"
        >
          <a-option>业绩</a-option>
        </a-select>
      </div>
    </div>
    <div class="data-chart-wrapper " >
         <div style="display: flex;justify-content: center;align-items: center;">
            <WaterProgressCircle
              :value="progress"
              size="200"
              :duration="2"
              :gradientColors="['#DBEAFE', '#3B82F6']"
              :outerStrokeWidth="1"
              innerText="时间进度"
              particleColorProp="#3B82F6  "
              outerStrokeColor="#3B82F6"
              :showOuter="false"
            />
         </div>

        <div class="data-view-model" >
            <BaiduLilCard v-for="(item, index) in cardList" :key="index"
            :popoverContent="item.title "
            :showPopover="item.showPopover"
            :title="item.title"
            :value="item.value"
            :icon="item.icon"/>
        </div>
        <div class="data-view-card" >
          <StatsCard
            cardWidth="100%"
            :data="statsData"
          />
        </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import BaiduLilCard from './baiduLilCard.vue';
import WaterProgressCircle from './baiduDataAll.vue';
import StatsCard from './StatsCard.vue';
import { isMobile } from '@/utils';
defineOptions({ name: 'BaiduCardData' });

const props = defineProps<{
  parentData?: Record<string, any>
}>();

const cardList = computed(() => [
  { title: props.parentData?.quarter+'总任务' ?? '', value: props.parentData?.totalTask ?? '-',  popoverContent: '当前季度的消耗' , showPopover: true ,icon: '/static/baiduIcon/task.png' },
  { title: '当前总消耗', value: props.parentData?.totalCost ?? '-', popoverContent: '这是弹窗内容' , showPopover: true , icon: '/static/baiduIcon/depleteAll.png' },
  { title: '昨日消耗', value: props.parentData?.yesterdayCost ?? '-', popoverContent: '这是弹窗内容' , showPopover: true , icon: '/static/baiduIcon/depleteYe.png' },
  { title: '近七日消费均值', value: props.parentData?.lastSevenAvgCost ?? '-', popoverContent: '这是弹窗内容' , showPopover: true , icon: '/static/baiduIcon/depleteSe.png' }
])

const sortType = ref('业绩');
const progress = computed(() => Number(props.parentData?.timeProgress ?? 0));

const statsData = computed(() => [
  {
    title: props.parentData?.firstTaskFinishRate ?? '-',
    icon: 'fa-tasks',
    iconImg:'/static/baiduIcon/finsh1.png',
    iconColor: '#3B82F6',
    stats: [
      { label: '任务数', value: props.parentData?.firstFinishTask ?? '-' },
      { label: '后续日均', value: props.parentData?.firstFinishTaskAvg ?? '-' },
      { label: '任务进度', value: props.parentData?.firstFinishTaskProgress ?? '-', isProgress: true }
    ]
  },
  {
    title: props.parentData?.secondTaskFinishRate ?? '-',
    icon: 'fa-check-square-o',
    iconImg:'/static/baiduIcon/finsh2.png',
    iconColor: '#10B981',
    stats: [
      { label: '任务数', value: props.parentData?.secondFinishTask ?? '-' },
      { label: '后续日均', value: props.parentData?.secondFinishTaskAvg ?? '-' },
      { label: '任务进度', value: props.parentData?.secondFinishTaskProgress ?? '-', isProgress: true }
    ]
  },
  {
    title: props.parentData?.thirdTaskFinishRate ?? '-',
    icon: 'fa-star',
    iconImg:'/static/baiduIcon/finsh3.png',
    iconColor: '#8B5CF6',
    stats: [
      { label: '任务数', value: props.parentData?.thirdFinishTask ?? '-' },
      { label: '后续日均', value: props.parentData?.thirdFinishTaskAvg ?? '-' },
      { label: '任务进度', value: props.parentData?.thirdFinishTaskProgress ?? '-', isProgress: true }
    ]
  }
]);
</script>
<style lang="scss">
@use '@/styles/var.scss' as *;
.header_top{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.select-wrapper {
  width: clamp(80px, 25%, 100px);
}
.data-chart-wrapper {
  display: flex;
  flex-direction: row;
  gap: 10px;

  > div:nth-child(1) {
    flex: 1;   /* 最小占比 */
  }
  > div:nth-child(2) {
    flex: 2;   /* 中等占比 */
  }
  > div:nth-child(3) {
    flex: 3;   /* 最大占比 */
  }
}



@media screen and (max-width: 1300px) {
  .data-chart-wrapper {
    flex-direction: column;
    > div {
      flex: unset; /* 竖排时恢复默认 */
    }
  }
}


.data-view-model {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* 两列 */
  gap: 10px;
  margin-right: 20px;
}
@media screen and (max-width: 1600px) {
  .data-view-model{
    margin-right: 0;
  }

}

.data-view-card{
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  width: 100%;
}
</style>