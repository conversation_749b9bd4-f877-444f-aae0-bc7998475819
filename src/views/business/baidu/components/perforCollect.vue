<template>
  <div class="gi_class">
    <a-card>
      <div class="filter-box">
        <!-- 一级行业 -->
        <div class="filter-item">
          <p>一级行业：</p>
          <a-select
            placeholder="请选择一级行业"
            v-model="formData.industry1"
            allow-clear
            allow-search
            @change="handleSelect"
            @clear="handleCustomerClear1r"
            multiple
          >
            <a-option
              v-for="(item,index) in industry1rList"
              :key="index"
              :value="item"
            >
              {{ item }}
            </a-option>
          </a-select>

        </div>
        <!-- 签约主体 -->
        <div class="filter-item">
          <p>签约主体：</p>
          <a-input
            class="range-picker"
            placeholder="请输入签约主体"
            v-model="formData.contractEntity"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            allow-clear
          ></a-input>
        </div>
        <!-- 广告主 -->
        <div class="filter-item">
          <p>广告主：</p>
          <a-input
            class="range-picker"
            placeholder="请输入广告主"
            v-model="formData.custName"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            allow-clear
          ></a-input>
        </div>
        <!-- 销售 -->
        <div class="filter-item">
          <p>销售：</p>
          <a-input
            class="range-picker"
            placeholder="请输入广告主"
            v-model="formData.sales"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            allow-clear
          ></a-input>
        </div>

      </div>
      <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 20px;">
        <div>
          消耗现金汇总：<span style="font-weight: bold;">{{formatNumber(totalCost)}}</span>
        </div>
        <a-button
          @click="exportData"
          type="primary"
          :loading="btnLoading"
          size="large"
        >导出</a-button>
      </div>
      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #cell-costAvg7="{ record }">
          {{ record.costAvg7 }}
        </template>
        <template #cell-costAvg30="{ record }">
          {{ record.costAvg30 }}
        </template>
      </GiTable>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'PerforCollect' });
import { ref, onMounted, reactive, watch } from 'vue';
import { collectExportListApi, exportListApi, getIndustry1rApi, performanceDetailApi } from '@/apis/agent';
import { Message } from '@arco-design/web-vue';
import { formatNumber } from '@/utils/formatNumber';
const props = defineProps<{
  externalDates?: string[];
}>();
console.log(props.externalDates, 'props');
const totalCost = ref(0);
const btnLoading = ref(false);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const dataList = ref([]);
// 固定列
const baseColumns = [
  {
    title: '签约主体',
    dataIndex: 'contractEntity',
    align: 'center',
    width: 200,
  },
  { title: '广告主', dataIndex: 'advertiserName', align: 'center', width: 200 },
  { title: '销售', dataIndex: 'salesName', align: 'center', width: 100 },
  { title: '行业', dataIndex: 'firstIndustry', align: 'center', width: 200 },
];
// 动态列（由接口返回时生成）
const columns = ref([...baseColumns]);
function buildColumns(dates: string[]) {
  // 插入日期列
  const dateColumns = dates.map((d) => ({
    title: d,
    dataIndex: d,
    align: 'center',
    width: 130,
  }));

  // 固定尾部列
  const tailColumns = [
    {
      title: '近七日消费均值',
      dataIndex: 'costAvg7',
      align: 'center',
      width: 150,
    },
    {
      title: '近三十日消费均值',
      dataIndex: 'costAvg30',
      align: 'center',
      width: 150,
    },
  ];

  columns.value = [...baseColumns, ...dateColumns, ...tailColumns];
}
function transformData(rawData, dates) {
  return rawData.map((item) => {
    const row: any = { ...item };
    // 把 cost 数组展开为 {date1: cost[0], date2: cost[1], ...} 并格式化
    dates.forEach((d, i) => {
      row[d] = formatNumber(item.cost[i]);
    });
    // 格式化尾部列
    row.costAvg7 = formatNumber(item.costAvg7);
    row.costAvg30 = formatNumber(item.costAvg30);
    return row;
  });
}
function getDetail() {
  loading.value = true;
  performanceDetailApi(formData.value).then((res) => {
    if (res.code === '0') {
      const dates = res.data.date;
      const list = res.data.data;
      // 动态生成 columns
      buildColumns(dates);
      // 转换数据
      dataList.value = transformData(list, dates);
      pagination.total = res.data.totalCount;
      totalCost.value = res.data.totalCash;
      loading.value = false;
    } else {
      Message.error(res.msg || '获取数据失败');
      loading.value = false;
    }
  });
}

const formData = ref({
  industry1: [],
  contractEntity: '',
  custName: '',
  sales: '',
  startDate: props.externalDates?.[0] || '',
  endDate: props.externalDates?.[1] || '',
  page: pagination.currentPage,
  pageSize: pagination.pageSize,
});
const industry1rList = ref([]);
async function getIndustry1r() {
  const res = await getIndustry1rApi();
  industry1rList.value = res.data.oneIndustryName;
}
function against(isSearch: number) {
  if(isSearch === 1){
    formData.value.page = 1;
    pagination.currentPage = 1;
  }
  performanceDetailApi(formData.value).then((res) => {
    console.log(formData.value,'formData');
    if (res.code === '0') {
      const dates = res.data.date;
      const list = res.data.data;
      // 动态生成 columns
      buildColumns(dates);
      // 转换数据
      dataList.value = transformData(list, dates);
      pagination.total = res.data.totalCount;
      totalCost.value = res.data.totalCash;
      loading.value = false;
    } else {
      Message.error(res.msg || '获取数据失败');
      loading.value = false;
    }
  });
}
function handleSelect(value) {
  getDetail();
  console.log(value, 'value');
}
function handleCustomerClear1r() {
  formData.value.industry1 = [];
}
function handleSearch() {
  const isSearch = ref(1);
  against(isSearch.value);
}

function handlePageChange(page) {
  pagination.currentPage = page;
  formData.value.page = page;
  console.log('当前页码:', page);
  getDetail();
}
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  formData.value.pageSize = pageSize;
  getDetail();
}
onMounted(() => {
  getIndustry1r();
});
// 导出
async function exportData() {
  // 校验是否有数据
  if (!dataList.value || dataList.value.length === 0) {
    Message.warning('暂无数据可导出');
    return;
  }

  btnLoading.value = true; // 开始 loading
  try {
    const res = await collectExportListApi(formData.value);
    // 检查res.data是否为Blob，且类型正确
    if (
      !(res.data instanceof Blob) ||
      !res.data.type.includes('spreadsheetml')
    ) {
      throw new Error('API返回的数据不是有效的Excel文件');
    }

    const blob = res.data;
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // 从响应头解析文件名（处理URL编码和默认值）
    let filename = '报表.xlsx';
    if (res.headers?.['content-disposition']) {
      const match = res.headers['content-disposition'].match(/filename=(.+)/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败详情:', e);
    Message.error(`导出失败: ${e.message}`);
  } finally {
    btnLoading.value = false;
  }
}
// Watch for changes in externalDates prop and update columns/data accordingly
watch(
  () => props.externalDates,
  (newVal) => {
    if (newVal && newVal.length === 2) {
      buildColumns(newVal);
      formData.value.startDate = newVal[0];
      formData.value.endDate = newVal[1];
      getDetail();
    }
  },
  { immediate: true }, // 只要 immediate 就能保证首次有值时触发
);
</script>

<style scoped>
.filter-box {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  grid-auto-rows: auto;
  gap: 16px 24px;
  width: 100%;
  align-items: start;
}

.filter-item {
  width: 100%;
  display: flex;
  align-items: start;
  flex-direction: column;
  gap: 8px;
}

/* 标签 */
.filter-item > p {
  margin: 0;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 控件撑满 */
.filter-item
  :is(.arco-picker, .arco-select, .arco-input-wrapper, .range-picker) {
  flex: 1;
  width: 100% !important;
}

/* 小屏：标签在上，控件在下 */
@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    align-items: stretch;
  }
  .filter-item > p {
    margin-bottom: 6px;
  }
}
</style>