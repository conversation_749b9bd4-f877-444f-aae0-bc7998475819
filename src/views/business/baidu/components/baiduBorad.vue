<template>
    <div class="gi_pages ">
        <a-card class="gi-card-title">
            <div >
                <BaiduCardData :parent-data="general"/>
            </div>
            <div style="margin-top: 20px;">
                <BaiduTrend :parent-data="general"/>
            </div>
            <!-- <div style="margin-top: 20px;">
                <BaiduTable/>
            </div> -->
            <div style="margin-top: 20px;">
                <NewClient/>
            </div>
        </a-card>
    </div>
</template>


<script lang="ts" setup>
import BaiduCardData from './baiduCardData.vue';
import BaiduTrend from './baiduTrend.vue';
import BaiduTable from './baiduTable.vue';
import NewClient from './newClient.vue';
import { onMounted } from 'vue';
defineOptions({ name: 'BaiduBorad' });
import { useGeneralDataStore } from '@/stores/generalData';
import { getBDCardDataApi } from '@/apis/agent';
const generalStore = useGeneralDataStore();
const general = ref()
onMounted(()=>{
    // fetchData()
    getBDCardData()
})
// async function fetchData() {
//   await generalStore.fetchBDCardData();
// }
async function getBDCardData(){
    const res = await getBDCardDataApi();
    general.value = res.data;
    console.log(res.data,'百度卡片数据ye')
}
</script>


<style lang="scss">
@use '@/styles/var.scss' as *;

</style>