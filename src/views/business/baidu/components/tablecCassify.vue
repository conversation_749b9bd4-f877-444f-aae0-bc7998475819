<template>
  <div>
    <div style="display: flex;flex-direction: row;gap: 10px;align-items: center; background-color: #fff;padding: 10px;">
      <div >
        <h4>查看纬度</h4>
        <div
          class="active-dimension"
          :class="theme"
          style="display: flex;"
        >
          <div class="dim-chip-group">
            <div
              v-for="item in dimensionList"
              :key="item.key"
              class="dim-chip"
              :class="{ active: item.key === activeDimension }"
              @click="handleDimensionChange(item.key)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <div v-if="activeDimension === 1">
        <h4>日期范围：</h4>
        <a-range-picker
            style="margin-top: 5px;"
          v-model="selectedDates"
          :time-picker-props="{defaultValue:['00:00:00','00:00:00']}"
          class="range-picker"
          :allow-clear="false"
          :shortcuts="rangeShortcuts"
        />
      </div>
    </div>

    <div>
      <PerforCollect
        v-if="activeDimension === 1"
        :external-dates="selectedDates"
      />
      <PerforTable
        v-if="activeDimension === 2"
      />
    </div>
  </div>

</template>
<script lang="ts" setup>
import { useAppStore } from '@/stores/useAppStore';
import { ref, computed } from 'vue';
defineOptions({ name: 'TableCassify' });
import PerforTable from './perforTable.vue';
import PerforCollect from './perforCollect.vue';
import dayjs from 'dayjs';
const rangeShortcuts = [
  {
    label: '七天',
    value: () => [
      dayjs().subtract(6, 'day').startOf('day'),
      dayjs().endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
  {
    label: '季度',
    value: () => {
      const now = dayjs();
      const quarterStart = now.startOf('quarter');
      const quarterEnd = now.endOf('quarter');
      // 如果当前日期超过当前季度结束日期，则用当前季度结束，否则用昨天
      const endDate = now.isAfter(quarterEnd) ? quarterEnd : now.subtract(1, 'day').endOf('day');
      return [quarterStart, endDate];
    },
  },
];
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
const dimensionList = [
  { key: 1, label: '汇总数据' },
  { key: 2, label: '分日数据' },
];
const activeDimension = ref(1);
const selectedDates = ref<string[]>([
  dayjs().subtract(7, 'day').startOf('day').format('YYYY-MM-DD'),
  dayjs().subtract(1, 'day').endOf('day').format('YYYY-MM-DD'),
]);
function handleDimensionChange(key: number) {
  activeDimension.value = key;
}
</script>
<style scoped>
.dim-chip-group {
  display: flex;
  flex-direction: row;
  margin-top: 5px;
  border: 1px solid #eee;
}
/* --- 维度选择 chips 的主题态 --- */
.active-dimension.light .dim-chip-group {
  border-color: #eee;
}
.active-dimension.dark .dim-chip-group {
  border-color: #2a2a2a;
}
.dim-chip {
  cursor: pointer;
  user-select: none;
  padding: 7px 15px;
  flex: 1;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #666;
  background: #fff;
  position: relative;
}
.active-dimension.light .dim-chip {
  color: #666;
  background: #fff;
}
.active-dimension.dark .dim-chip {
  color: #cfcfcf;
  background: #1f1f1f;
}

.active-dimension.light .dim-chip:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}
.active-dimension.dark .dim-chip:hover:not(.active) {
  background: #2a3344;
  color: #9ec2ff;
}

/* active 选中态两边主题一致，用主色 */
.active-dimension.light .dim-chip.active,
.active-dimension.dark .dim-chip.active {
  background: #2d80ff;
  color: #fff;
}
</style>