<template>
    <div>
        <div style="display: flex;flex-direction: row;align-items: center;justify-content: space-between;">
            <h3>任务总揽（元）</h3>
            <div class="select-wrapper" >
                <a-select v-model="sortType" placeholder="请选择">
                    <a-option>业绩</a-option>
                </a-select>
            </div>
        </div>
        <div>
            <GiTable
                :data="dataList"
                :columns="columns"
                :loading="loading"
                :pagination="pagination"
                :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
                row-key="id"
                v-model:selectedKeys="selectedKeys"
                :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']">
            </GiTable>
        </div>
    </div>
</template>
<script lang="ts" setup>
defineOptions({ name: 'BaiduTable' });
const sortType = ref('业绩');
const loading = ref(false)
const pagination = reactive({
  currentPage: 1,           // 当前页
  pageSize: 10,         // 每页条数
  total: 0,             // 总数（在接口返回后更新）
  showTotal: true,      // 显示总数
  showPageSize: true,   // 显示每页条数选择器
  showJumper: true      // 显示跳转页码输入框
})
const selectedKeys = ref([])
const columns = [
    { title: '客户名称', dataIndex: 'customerName',minWidth: 100, ellipsis: true, tooltip: true },
    { title: '客户简称', dataIndex: 'abbreviation', minWidth: 100, ellipsis: true, tooltip: true },
    { title: 'QTD收入', dataIndex: 'QTD',  minWidth: 100, ellipsis: true, tooltip: true },
    { title: '昨日收入', dataIndex: 'yesterdayIncome',  minWidth: 100, ellipsis: true, tooltip: true },
    { title: '收入日同比', dataIndex: 'onYear', slotName: 'onYear', minWidth: 100, ellipsis: true, tooltip: true },
    { title: '收入日环比', dataIndex: 'QOQ', slotName: 'QOQ', minWidth: 100, ellipsis: true, tooltip: true },
]
const dataList = ref([
  { customerName: '北京恒通科技有限公司', abbreviation: '恒通科技', QTD: 1250000, yesterdayIncome: 15200, onYear: 12.5, QOQ: 8.3 },
  { customerName: '上海星辰贸易集团', abbreviation: '星辰贸易', QTD: 980000, yesterdayIncome: 11800, onYear: -3.2, QOQ: 5.7 },
  { customerName: '广州未来信息技术有限公司', abbreviation: '未来信息', QTD: 1560000, yesterdayIncome: 18900, onYear: 15.8, QOQ: 10.2 },
  { customerName: '深圳创新发展有限公司', abbreviation: '深圳创新', QTD: 870000, yesterdayIncome: 9500, onYear: 7.6, QOQ: -2.1 },
  { customerName: '杭州智慧商业集团', abbreviation: '智慧商业', QTD: 1120000, yesterdayIncome: 13400, onYear: 9.3, QOQ: 4.5 },
  { customerName: '成都天府数据服务有限公司', abbreviation: '天府数据', QTD: 760000, yesterdayIncome: 8900, onYear: -1.8, QOQ: 3.2 },
  { customerName: '武汉长江电子科技有限公司', abbreviation: '长江电子', QTD: 1320000, yesterdayIncome: 16500, onYear: 11.2, QOQ: 7.8 },
  { customerName: '南京紫金投资集团', abbreviation: '紫金投资', QTD: 950000, yesterdayIncome: 10800, onYear: 6.5, QOQ: -1.3 },
  { customerName: '西安古都网络科技有限公司', abbreviation: '古都网络', QTD: 680000, yesterdayIncome: 7900, onYear: 3.7, QOQ: 2.9 },
  { customerName: '重庆山城商业发展有限公司', abbreviation: '山城商业', QTD: 1050000, yesterdayIncome: 12300, onYear: 8.9, QOQ: 6.4 },
  { customerName: '青岛海洋科技集团', abbreviation: '海洋科技', QTD: 820000, yesterdayIncome: 9700, onYear: -2.5, QOQ: 1.8 },
  { customerName: '天津渤海贸易有限公司', abbreviation: '渤海贸易', QTD: 1180000, yesterdayIncome: 14200, onYear: 10.5, QOQ: 8.1 },
  { customerName: '苏州工业园区信息技术有限公司', abbreviation: '园区信息', QTD: 1450000, yesterdayIncome: 17600, onYear: 13.2, QOQ: 9.5 },
  { customerName: '郑州中原商业集团', abbreviation: '中原商业', QTD: 790000, yesterdayIncome: 9200, onYear: 4.8, QOQ: -0.7 },
  { customerName: '长沙湘江智慧科技有限公司', abbreviation: '湘江智慧', QTD: 1020000, yesterdayIncome: 12100, onYear: 7.3, QOQ: 5.2 }
])
</script>