<template>
  <div class=" baidu-trend " :style="isMobile()?'text-align: center;':'text-align: left;'">
    <h2>季度消耗趋势</h2>
    <div class="chart-container">
      <div ref="trendChart" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch, onBeforeUnmount, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useAppStore } from '@/stores/useAppStore'
import { isMobile } from '@/utils'
defineOptions({ name: 'BaiduTrend' })
const trendChart = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const appStore = useAppStore()
// 读取主题状态
const theme = computed(() => appStore.theme)

const props = defineProps<{
  parentData?: Record<string, any>
}>();

function getMockData(themeValue: string) {
  return {
    xAxis: {
      type: 'category',
      name:isMobile()? '': '日期',
      data: props.parentData?.dateList ?? [],
      boundaryGap: false // 1. tighter fit
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter:isMobile()? '{value}': '{value} 元',
        color: '#999'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#e0e0e0'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#bbb'
        }
      },
      axisTick: {
        show: true
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#aaa',
          type: 'dashed'
        }
      }
    },
    legend: {
      show: true,
      top: 0,
      left: 'center'
    },
    grid: {
      left:isMobile()?'20%': '7%',
      right:isMobile()?'8%': '3%',
      top: 40, // leave space for legend
    },
    series: [
      {
        name: '全部产品线',
        data: props.parentData?.costList ?? [],
        type: 'line',
        smooth: true,
        lineStyle: {
          color: 'rgb(75, 192, 192)'
        },
        areaStyle: {
          color: 'rgba(75,192,192,0.08)'
        },
        symbol: 'circle',
        symbolSize: 6
      }
    ],
    dataZoom: [
      { type: 'slider', start: 0, end: 100 },
      { type: 'inside', start: 0, end: 100 }
    ]
  }
}
function setChartData() {
  if (!chartInstance) return;
  const date = props.parentData?.dateList ?? [];
  const cost = props.parentData?.costList ?? [];
  chartInstance.setOption({
    xAxis: { data: date },
    series: [{ name: '全部产品线', data: cost }]
  });
}
function initChart() {
  if (trendChart.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(trendChart.value, theme.value === 'dark' ? '' : 'light')
    const option = getMockData(theme.value)
    chartInstance.setOption(option)
  }
}
watch(
  () => props.parentData,
  async (newVal) => {
    if (!newVal) return;
    await nextTick();
    if (!chartInstance) {
      initChart();
    }
    setChartData();
  },
  { immediate: true, deep: true, flush: 'post' }
);
onMounted(() => {
  initChart()
})
watch(theme, () => {
  initChart();
  setChartData();
})
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<style scoped lang="scss">
.baidu-trend {
//   background-color: #fff;
    border-radius: 8px;

  h2 {
    margin-bottom: 16px;
    font-weight: 600;
    // color: #333;
  }

  .chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    // background-color: #000;
  }
}
</style>
