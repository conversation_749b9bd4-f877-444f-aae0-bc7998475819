<template>
  <div class="gi_class">
    <a-card>

      <div class="filter-box">

        <!-- 日期范围 -->
        <div class="filter-item">
          <p>日期范围：</p>
          <a-range-picker
            v-model="formData.date"
            :time-picker-props="{
                        defaultValue:['00:00:00','00:00:00']
                        }"
            @change="onChange"
            class="range-picker"
            :allow-clear="false"
            :shortcuts="rangeShortcuts"
          />
        </div>
        <!-- 一级行业 -->
        <div class="filter-item">
          <p>一级行业：</p>
          <a-select
            placeholder="请选择一级行业"
            v-model="formData.firstIndustry"
            allow-clear
            allow-search
            @change="handleSelect"
            @clear="handleCustomerClear1r"
            multiple
          >
            <a-option
              v-for="(item,index) in industry1rList"
              :key="index"
              :value="item"
            >
              {{ item }}
            </a-option>
          </a-select>

        </div>
        <!-- 二级行业 -->
        <div class="filter-item">
          <p>二级行业：</p>
          <a-select
            placeholder="请选择一级行业"
            v-model="formData.secondIndustry"
            allow-clear
            allow-search
            @change="handleSelect"
            @clear="handleCustomerClear2r"
            multiple
          >
            <a-option
              v-for="(item,index) in industry2rList"
              :key="index"
              :value="item"
            >
              {{ item }}
            </a-option>
          </a-select>
        </div>
        <!-- 广告主体 -->
        <div class="filter-item">
          <p>广告主：</p>
          <a-input
            class="range-picker"
            placeholder="请输入广告主"
            v-model="formData.custName"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            allow-clear
          ></a-input>
        </div>
      </div>
      <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 20px;">
        <div>
          消耗现金汇总：<span style="font-weight: bold;">{{formatNumber(totalCost)}}</span>
        </div>
        <a-button
          type="primary"
          @click="exportData"
          :loading="btnLoading"
          size="large"
        >导出</a-button>
      </div>

      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #cell-performance="{ record }">
          {{ formatNumber(record.performance) }}
        </template>
      </GiTable>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import {
  exportListApi,
  getIndustry1rApi,
  getPerformanceListApi,
  performanceDetailApi,
} from '@/apis/agent';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import { ref, onMounted, computed } from 'vue';
import { formatNumber } from '@/utils/formatNumber';
defineOptions({ name: 'PerforTable' });
import { useAppStore } from '@/stores/useAppStore';
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
const dimensionList = [
  { key: 1, label: '汇总数据' },
  { key: 2, label: '分日数据' },
];
const activeDimension = ref(1);
function handleDimensionChange(key: number) {
  activeDimension.value = key;
  formData.value.dataDimension = key;
}
// 默认获取昨天的起止时间并格式化为 yyyy.MM.dd
function formatDate(date: Date) {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}
const rangeShortcuts = [
  {
    label: '今天',
    value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '昨天',
    value: () => [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '七天',
    value: () => [
      dayjs().subtract(6, 'day').startOf('day'),
      dayjs().endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];

const now = new Date();
const yesterday = new Date(now);
yesterday.setDate(now.getDate() - 1);

const yesterdayStart = new Date(yesterday);
yesterdayStart.setHours(0, 0, 0, 0);

const yesterdayEnd = new Date(yesterday);
yesterdayEnd.setHours(23, 59, 59, 999);

const formData = ref({
  date: [
    formatDate(dayjs().subtract(7, 'day').startOf('day').toDate()),
    formatDate(dayjs().subtract(1, 'day').endOf('day').toDate()),
  ],
  custName: '',
  firstIndustry: [],
  secondIndustry: [],
  dataDimension: 1,
});
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const advertiserName = ref('');
const dataList = ref([]);
const columns = [
  { dataIndex: 'dateFlag', title: '日期', width: 130, align: 'center' },
  { dataIndex: 'custName', title: '广告主', width: 130, align: 'center' },
  {
    dataIndex: 'customercrmname',
    title: '消费广告主',
    width: 130,
    align: 'center',
  },
  { dataIndex: 'agentName', title: '代理公司', width: 130, align: 'center' },
  {
    dataIndex: 'performance',
    title: '业绩',
    width: 130,
    align: 'center',
    slotName: 'cell-performance',
  },
  {
    dataIndex: 'consumptionType',
    title: '业绩类别',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'ssgTradeName1New',
    title: 'MEG一级行业',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'ssgTradeName2New',
    title: 'MEG二级行业',
    width: 130,
    align: 'center',
  },
  { dataIndex: 'contractNo', title: '合同号', width: 130, align: 'center' },
  { dataIndex: 'framePolicyNo', title: '框架号', width: 130, align: 'center' },
  {
    dataIndex: 'agentGroupName',
    title: '代理集团',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'agentAlbCode',
    title: '代理商ALB code',
    width: 130,
    align: 'center',
  },
  { dataIndex: 'prodlinename', title: '产品线', width: 130, align: 'center' },
  { dataIndex: 'prodlinename', title: '产品线', width: 130, align: 'center' },
  {
    dataIndex: 'monitorProductName',
    title: '产品线大类',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'prodlineName2nd',
    title: '产品线小类',
    width: 130,
    align: 'center',
  },
  { dataIndex: 'salesmgrname', title: '客户经理', width: 130, align: 'center' },
  { dataIndex: 'channleName', title: '渠道经理', width: 130, align: 'center' },
  {
    dataIndex: 'customercrmalbcode',
    title: '消费广告主ALB code',
    width: 130,
    align: 'center',
  },
];
const loading = ref(false);
const selectedKeys = ref([]); // 选中行的key

let getPerformanceListLoading = false;
let getPerformanceListDebounceTimer: ReturnType<typeof setTimeout> | null =
  null;
const totalCost = ref(0);
async function getPerformanceList() {
  if (getPerformanceListLoading) return;
  getPerformanceListLoading = true;
  loading.value = true;
  try {
    const res = await getPerformanceListApi({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...formData.value,
    });
    dataList.value = res.data.pageList.records;
    pagination.total = res.data.pageList.total;

    totalCost.value = res.data.totalCost;
    console.log(res, '业绩报表');
  } finally {
    getPerformanceListLoading = false;
    loading.value = false;
  }
}

const btnLoading = ref(false);

async function exportData() {
  // 校验是否有数据
  if (!dataList.value || dataList.value.length === 0) {
    Message.warning('暂无数据可导出');
    return;
  }

  btnLoading.value = true; // 开始 loading
  try {
    const res = await exportListApi(formData.value);
    // 检查res.data是否为Blob，且类型正确
    if (
      !(res.data instanceof Blob) ||
      !res.data.type.includes('spreadsheetml')
    ) {
      throw new Error('API返回的数据不是有效的Excel文件');
    }

    const blob = res.data;
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // 从响应头解析文件名（处理URL编码和默认值）
    let filename = '报表.xlsx';
    if (res.headers?.['content-disposition']) {
      const match = res.headers['content-disposition'].match(/filename=(.+)/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败详情:', e);
    Message.error(`导出失败: ${e.message}`);
  } finally {
    btnLoading.value = false;
  }
}

// Debounced version for search input
function getPerformanceListDebounced() {
  if (getPerformanceListDebounceTimer)
    clearTimeout(getPerformanceListDebounceTimer);
  getPerformanceListDebounceTimer = setTimeout(() => {
    getPerformanceList();
  }, 300); // debounce delay (ms)
}

// Update handleSearch and onChange to use debounce
function handleSearch() {
  getPerformanceListDebounced();
}

function onChange(date: any, dateString: any) {
  getPerformanceListDebounced();
}

function handlePageChange(page) {
  pagination.currentPage = page;
  console.log('当前页码:', page); // 确认是否打印正确页码（如点击第2页应打印 2）
  getPerformanceList();
}
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  getPerformanceList();
}
// 行业下拉
const industry1rList = ref([]);
const industry2rList = ref([]);
async function getIndustry1r() {
  const res = await getIndustry1rApi();
  industry1rList.value = res.data.oneIndustryName;
  industry2rList.value = res.data.twoIndustryName;
  console.log(res, '一级行业');
}
function handleSelect(value) {
  getPerformanceList();
}
function handleCustomerClear2r() {
  formData.value.secondIndustry = [];
  getPerformanceList();
}
function handleCustomerClear1r() {
  formData.value.firstIndustry = [];
  getPerformanceList();
}
onMounted(() => {
  getPerformanceList();
  getIndustry1r();
});
</script>

<style lang="scss" scoped>
@use '@/styles/var.scss' as *;
.filter-box {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr)); // 两列，等分撑满
  grid-auto-rows: auto;
  gap: 16px 24px; // 行间距16，列间距24
  width: 100%;
  align-items: start; // 顶对齐，避免高度不一致时拉扯
}

/* 小屏：改为单列 */
@media (max-width: 768px) {
  .filter-box {
    grid-template-columns: 1fr;
  }
}

/* 每个项内部：左边标签，右边控件（控件自适应占满） */
/* 每个筛选项：标签 + 控件横向排列 */
.filter-item {
  width: 100%;
  display: flex;
  align-items: start;
  flex-direction: column;
  gap: 8px;
}

/* 标签 */
.filter-item > p {
  margin: 0;
  white-space: nowrap;
  flex-shrink: 0; // 标签不挤压
}

/* 控件撑满 */
.filter-item
  :is(.arco-picker, .arco-select, .arco-input-wrapper, .range-picker) {
  flex: 1;
  width: 100% !important;
}

/* 小屏：标签在上，控件在下 */
@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    align-items: stretch;
  }
  .filter-item > p {
    margin-bottom: 6px;
  }
}

/* --- 维度选择 chips 的主题态 --- */
.active-dimension.light .dim-chip-group {
  border-color: #eee;
}
.active-dimension.dark .dim-chip-group {
  border-color: #2a2a2a;
}

.active-dimension.light .dim-chip {
  color: #666;
  background: #fff;
}
.active-dimension.dark .dim-chip {
  color: #cfcfcf;
  background: #1f1f1f;
}

.active-dimension.light .dim-chip:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}
.active-dimension.dark .dim-chip:hover:not(.active) {
  background: #2a3344;
  color: #9ec2ff;
}

/* active 选中态两边主题一致，用主色 */
.active-dimension.light .dim-chip.active,
.active-dimension.dark .dim-chip.active {
  background: #2d80ff;
  color: #fff;
}
</style>