<template>
    <div class="gi_page">
        <div>
            <a-button @click="getDeplete(item.id)"
             v-for="item in btnList"
             :key="item.id"
             :type="item.id === type ? 'primary' : 'secondary'"
             :style="item.id === 1?'margin-right: 10px;':''"
             >{{ item.lable }}</a-button>
        </div>
        <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="false"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
        <template #cell-cost="{ record }">
          {{ formatNumber(record.cost) }}
        </template>
        <template #cell-dailyConsumptionChangeAmount="{ record }">
          {{ formatNumber(record.dailyConsumptionChangeAmount) }}
        </template>
        </GiTable>
    </div>
</template>
<script lang="ts" setup>
import { getDepleteApi } from '@/apis/agent';
import { formatNumber } from '@/utils/formatNumber';
    defineOptions({ name: 'DepleteTop' });
    const btnList = [
        { id: 1, lable: '电商客户' },
        { id: 2, lable: '非电商客户' },
    ]
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'promotionEntity', title: '客户名称', width: 130, align: 'center' },
        { dataIndex: 'cost', title: '消耗金额', width: 130, align: 'center' ,slotName:'cell-cost' },
        { dataIndex: 'dailyConsumptionChangeAmount', title: '变化金额', width: 130, align: 'center' ,slotName:'cell-dailyConsumptionChangeAmount' },
        { dataIndex: 'sales', title: '销售', width: 130, align: 'center' },
    ]
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    const type = ref(1);
    async function getDeplete(id){
        type.value = id;
        loading.value = true;
        const res = await getDepleteApi(id);
        dataList.value = res.data;
        loading.value = false;
    }
    onMounted(()=>{
        getDeplete(1)
    })
</script>
<style>
.gi_page{
    padding-bottom: 50px;
}
</style>