<template>
    <div class="gi_page">
        <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="false"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
        <template #cell-cost="{ record }">
          {{ formatNumber(record.cost) }}
        </template>
        <template #cell-dailyConsumptionChangeAmount="{ record }">
          {{ formatNumber(record.dailyConsumptionChangeAmount) }}
        </template>
        <template #cell-theoreticalCost="{ record }">
          {{ formatNumber(record.theoreticalCost) }}
        </template>
        </GiTable>
    </div>
</template>
<script lang="ts" setup>
    import { getDepleteApi } from '@/apis/agent';
    import { formatNumber } from '@/utils/formatNumber';
    defineOptions({ name: 'ImpMonitor' });
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'promotionEntity', title: '客户名称', width: 130, align: 'center' },
        { dataIndex: 'cost', title: '消耗金额', width: 130, align: 'center' ,slotName:'cell-cost' },
        { dataIndex: 'dailyConsumptionChangeAmount', title: '变化金额', width: 130, align: 'center' ,slotName:'cell-dailyConsumptionChangeAmount' },
        { dataIndex: 'sales', title: '销售', width: 130, align: 'center' },
        { dataIndex: 'theoreticalCost', title: '理论消耗', width: 130, align: 'center' ,slotName:'cell-theoreticalCost' },
    ]
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    async function getDeplete(){
        loading.value = true;
        const res = await getDepleteApi(3);
        dataList.value = res.data;
        loading.value = false;
    }
    onMounted(()=>{
        getDeplete()
    })
</script>
<style>
.gi_page{
    padding-bottom: 50px;
}
</style>