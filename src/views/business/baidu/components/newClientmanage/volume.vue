<template>
    <div class="gi_page">
        <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="false"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
        <template #cell-yesterdayPerformance="{ record }">
          {{ formatNumber(record.yestodayPerformance) }}
        </template>
        <template #cell-qbtPerformance="{ record }">
          {{ formatNumber(record.qbtPerformance) }}
        </template>
        <template #cell-speedTarget="{ record }">
          {{ formatNumber(record.speedTarget) }}
        </template>
        </GiTable>
    </div>
</template>
<script lang="ts" setup>
    import { getNewVolumeApi } from '@/apis/agent';
    import { formatNumber } from '@/utils/formatNumber';
    defineOptions({ name: 'Volume' });
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'customerName', title: '广告主', width: 130, align: 'center' },
        { dataIndex: 'yestodayPerformance', title: '昨日业绩', width: 130, align: 'center' ,slotName:'cell-yesterdayPerformance' },
        { dataIndex: 'qbtPerformance', title: 'QTD业绩', width: 130, align: 'center' ,slotName:'cell-qbtPerformance' },
        { dataIndex: 'speedTarget', title: '增速目标', width: 130, align: 'center' ,slotName:'cell-speedTarget' },
        { dataIndex: 'sales', title: '销售', width: 130, align: 'center' },
    ]
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    async function getDeplete(){
        loading.value = true;
        const res = await getNewVolumeApi();
        dataList.value = res.data;
        loading.value = false;

    }
    onMounted(()=>{
        getDeplete()
    })
</script>
<style>
.gi_page{
    padding-bottom: 50px;
}
</style>