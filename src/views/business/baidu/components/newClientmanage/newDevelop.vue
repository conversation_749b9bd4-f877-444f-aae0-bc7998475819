<template>
  <div
    class="new-customer-management"
    :class="theme"
  >
    <!-- 状态筛选器 - 仅在新客开发标签下显示 -->
    <div
      v-if="activeTopTab === 1"
      class="status-filter-container"
    >
      <div class="status-filter-buttons">
        <a-button
          :class="['status-btn', activeSubTab === 1 ? 'status-btn-active' : '']"
          @click="handleStatusChange(1)"
        >
          确认完成
        </a-button>
        <a-button
          :class="['status-btn', activeSubTab === 2 ? 'status-btn-active' : '']"
          @click="handleStatusChange(2)"
        >
          有望完成
        </a-button>
        <a-button
          :class="['status-btn', activeSubTab === 3 ? 'status-btn-active' : '']"
          @click="handleStatusChange(3)"
        >
          待观察
        </a-button>
      </div>
    </div>
    <!-- 数据表格区域 -->
    <div class="table-container">
      <GiTable
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="id"
        :loading="loading"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #cell-qbtPerformance="{ record }">
          {{ formatNumber(record.yesterdayPerformance) }}
        </template>
        <template #cell-yesterdayPerformance="{ record }">
          {{ formatNumber(record.qbtPerformance) }}
        </template>
      </GiTable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, defineExpose } from 'vue';
import type { ComputedRef } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import { getNewReportApi } from '@/apis/agent';
import { formatNumber } from '@/utils/formatNumber';
interface TableRecord {
  id: number;
  advertiser: string;
  yesterdayConsumption: number;
  qtdPerformance: number;
  salesPerson: string;
  status: 'active' | 'inactive' | 'new' | 'potential';
}

const appStore = useAppStore();
const theme: ComputedRef<string> = computed(() => appStore.theme);

// 顶部标签状态
const activeTopTab = ref<number>(1);

// 次级状态标签
const activeSubTab = ref<number>(1);
// 表格加载状态
const loading = ref<boolean>(false);

// 搜索和筛选条件
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});

// 表格列定义
const columns = [
  { dataIndex: 'customerName', title: '广告主', width: 130, align: 'center' },
  {
    dataIndex: 'yesterdayPerformance',
    title: '昨日消耗',
    width: 130,
    align: 'center',
    slotName: 'cell-yesterdayPerformance',
  },
  {
    dataIndex: 'qbtPerformance',
    title: 'QTD业绩',
    width: 130,
    align: 'center',
    slotName: 'cell-qbtPerformance',
  },
  { dataIndex: 'sale', title: '销售', width: 130, align: 'center' },
];

// 表格数据
const tableData = ref<TableRecord[]>([]);

// 获取表格数据的函数
const fetchTableData = async () => {
  loading.value = true;
  const res = await getNewReportApi(activeSubTab.value);
  tableData.value = res.data.xkData;
  loading.value = false;
};

const refreshTable = () => {
  fetchTableData();
};

// 处理状态筛选变化
const handleStatusChange = (key: number) => {
  activeSubTab.value = key;
  pagination.currentPage = 1;
  fetchTableData();
};

defineExpose({ refreshTable });

// 初始化时加载数据
onMounted(() => {
  handleStatusChange(1);
});
</script>

<style scoped>
.new-customer-management {
  /* padding: 24px; */
  background-color: var(--color-bg-1, #f5f7fa);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-1, #1d2129);
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.export-btn {
  background-color: var(--color-primary-6, #165dff);
  border-color: var(--color-primary-6, #165dff);
  color: var(--color-white, #fff);
}

.top-tabs {
  /* margin-bottom: 24px; */
  /* background-color: var(--color-bg-2, #fff); */
  border-radius: 4px;
  /* padding: 0 16px; */
}

/* 状态筛选器样式 */
.status-filter-container {
  display: flex;
  align-items: center;
  margin-top: 35px;
  border-radius: 4px;
}

.status-filter-buttons {
  display: flex;
  gap: 8px;
}

.status-btn {
  border-radius: 6px;
  padding: 6px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  /* background-color: var(--color-bg-1, #f5f7fa); */
  color: var(--color-text-3, #4e5969);
}

.status-btn-active {
  background-color: var(--color-primary-6, #165dff);
  color: var(--color-white, #fff);
  border-color: var(--color-primary-6, #165dff);
  font-weight: bold;
}

/* 数据概览卡片 */
.stats-cards {
  display: flex;
  gap: 24px;
  /* margin-bottom: 24px; */
  flex-wrap: wrap;
}

.stat-card {
  flex: 1;
  min-width: 240px;
  background-color: var(--color-bg-2, #fff);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 20px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-title {
  font-size: 14px;
  color: var(--color-text-3, #86909c);
  margin-bottom: 12px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-text-1, #1d2129);
  margin-bottom: 8px;
}

.stat-trend {
  font-size: 13px;
  display: flex;
  align-items: center;
}

.stat-trend.positive {
  color: var(--color-success-6, #00b42a);
}

.stat-trend.negative {
  color: var(--color-danger-6, #f53f3f);
}

.trend-compare {
  color: var(--color-text-3, #86909c);
  margin-left: 4px;
}

/* 筛选区域 */
.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.table-container {
  background-color: var(--color-bg-2, #fff);
  border-radius: 4px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); */
  overflow: hidden;
}

/* 广告主单元格样式 */
.advertiser-cell {
  display: flex;
  align-items: center;
}

.advertiser-logo {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.advertiser-name {
  font-weight: 500;
  color: var(--color-text-1, #1d2129);
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background-color: var(--color-primary-1, #e8f3ff);
  color: var(--color-primary-6, #165dff);
}

.status-inactive {
  background-color: var(--color-bg-3, #f2f3f5);
  color: var(--color-text-3, #86909c);
}

.status-new {
  background-color: var(--color-success-1, #eaffea);
  color: var(--color-success-6, #00b42a);
}

.status-potential {
  background-color: var(--color-warning-1, #fff7e8);
  color: var(--color-warning-6, #ff7d00);
}

.status-unknown {
  background-color: var(--color-danger-1, #fef0f0);
  color: var(--color-danger-6, #f53f3f);
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  align-items: center;
}

.view-btn {
  color: var(--color-primary-6, #165dff);
}

.edit-btn {
  color: var(--color-warning-6, #ff7d00);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stats-cards {
    flex-direction: column;
  }

  .filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }

  .status-filter-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-label {
    margin-bottom: 12px;
  }

  .status-filter-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .status-btn {
    flex: 1;
    text-align: center;
  }
}
</style>
