<template>
  <div class="gi_class">
    <a-card>
      <div class="filter-box">
        <div class="filter-item">
          <p>日期范围：</p>
          <a-range-picker
            v-model="formData.date"
            :time-picker-props="{
                        defaultValue:['00:00:00','00:00:00']
                        }"
            @change="onChange"
            @select="onSelect"
            class="range-picker"
            :allow-clear="false"
            :shortcuts="rangeShortcuts"
          />
        </div>
        <div class="filter-item">
          <p>广告主：</p>
          <a-input
            class="range-picker"
            placeholder="请输入广告主"
            v-model="formData.custName"
            @input="handleSearch"
            @clear="handleSearch"
            @pressEnter="handleSearch"
            allow-clear
          ></a-input>
        </div>
      </div>
      <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 20px;">
        <div >
          消耗现金汇总：<span style="font-weight: bold;">{{formatNumber(totalCost)}}</span>
        </div>
        <a-button
          type="primary"
          @click="exportData"
          :loading="btnLoading"
          size="large"
        >导出</a-button>
      </div>
      <!-- <a-button
        type="primary"
        style="margin-top: 20px;"
        @click="exportData"
        :loading="btnLoading"
        size="large"
      >导出</a-button> -->
      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
      <template #cell-payAmount1d="{ record }">
        {{ formatNumber(record.payAmount1d) }}
      </template>
      <template #cell-cashAmount1d="{ record }">
        {{ formatNumber(record.cashAmount1d) }}
      </template>
      <template #cell-totalCashBalance="{ record }">
        {{ formatNumber(record.totalCashBalance) }}
      </template>
      </GiTable>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import { accountExportListApi, exportListApi, getAccountListApi } from '@/apis/agent';
import { formatNumber } from '@/utils/formatNumber';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';
defineOptions({ name: 'AccountTable' });
const advertiserName = ref('');
// 默认获取昨天的起止时间并格式化为 yyyy.MM.dd
function formatDate(date: Date) {
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
}
const rangeShortcuts = [
  {
    label: '今天',
    value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '昨天',
    value: () => [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '七天',
    value: () => [
      dayjs().subtract(6, 'day').startOf('day'),
      dayjs().endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];
const now = new Date();
const yesterday = new Date(now);
yesterday.setDate(now.getDate() - 1);

const yesterdayStart = new Date(yesterday);
yesterdayStart.setHours(0, 0, 0, 0);

const yesterdayEnd = new Date(yesterday);
yesterdayEnd.setHours(23, 59, 59, 999);

const formData = ref({
  date: [formatDate(yesterdayStart), formatDate(yesterdayEnd)],
  custName: '',
});
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const dataList = ref([]);
const columns = [
  { dataIndex: 'dateFlag', title: '日期', width: 130, align: 'center' },
  { dataIndex: 'id', title: '账户ID', width: 130, align: 'center' },
  { dataIndex: 'ucName', title: '账户名称', width: 130, align: 'center' },
  { dataIndex: 'custName', title: '广告主', width: 130, align: 'center' },
  { dataIndex: 'agentName', title: '代理公司', width: 130, align: 'center' },

  { dataIndex: 'payAmount1d', title: '消耗', width: 130, align: 'center', slotName: 'cell-payAmount1d' },

  { dataIndex: 'cashAmount1d', title: '现金', width: 130, align: 'center', slotName: 'cell-cashAmount1d' },
  { dataIndex: 'prodTypeName',title: '产品线大类',width: 130,align: 'center'},
  { dataIndex: 'prodDetailTypeName',title: '产品线小类',width: 130,align: 'center'},
  { dataIndex: 'totalCashBalance',title: '账户总现金余额',width: 130,align: 'center',slotName: 'cell-totalCashBalance'},
  { dataIndex: 'ucId', title: '账户ID', width: 130, align: 'center' },
  { dataIndex: 'orderrowId', title: '订单行', width: 130, align: 'center' },
  { dataIndex: 'contractNo', title: '合同号', width: 130, align: 'center' },
  { dataIndex: 'custId', title: '资质客户ID', width: 130, align: 'center' },
  { dataIndex: 'custShortName',title: '客户简称',width: 130,align: 'center' },
  { dataIndex: 'agentAlbCode',title: '代理商ALB code',width: 130,align: 'center'},
  { dataIndex: 'agentGroupName',title: '代理集团',width: 130,align: 'center'},
  { dataIndex: 'salesManagerName',title: '客户经理', width: 130,align: 'center'},
  { dataIndex: 'channelManagerName',title: '渠道经理',width: 130,align: 'center'},
  { dataIndex: 'megCustTradeName1st',title: 'MEG账户一级行业',width: 130,align: 'center'},
  { dataIndex: 'megCustTradeName2nd',title: 'MEG账户二级行业', width: 130,align: 'center'},
  { dataIndex: 'custTradeName1st',title: 'MEG客户一级行业',width: 130,align: 'center'},
  { dataIndex: 'custTradeName2nd',title: 'MEG客户二级行业', width: 130,align: 'center'},
];
const loading = ref(false);
const selectedKeys = ref([]); // 选中行的key

let getPerformanceListLoading = false;
let getPerformanceListDebounceTimer: ReturnType<typeof setTimeout> | null =
  null;

const btnLoading = ref(false);
async function exportData() {
  // 校验是否有数据
  if (!dataList.value || dataList.value.length === 0) {
    Message.warning('暂无数据可导出');
    return;
  }

  btnLoading.value = true; // 开始 loading
  try {
    const res = await accountExportListApi(formData.value);
    // 检查res.data是否为Blob，且类型正确
    if (
      !(res.data instanceof Blob) ||
      !res.data.type.includes('spreadsheetml')
    ) {
      throw new Error('API返回的数据不是有效的Excel文件');
    }

    const blob = res.data;
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // 从响应头解析文件名（处理URL编码和默认值）
    let filename = '报表.xlsx';
    if (res.headers?.['content-disposition']) {
      const match = res.headers['content-disposition'].match(/filename=(.+)/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败详情:', e);
    Message.error(`导出失败: ${e.message}`);
  } finally {
    btnLoading.value = false;
  }
}
const totalCost = ref(0);
async function getPerformanceList() {
  if (getPerformanceListLoading) return;
  getPerformanceListLoading = true;
  loading.value = true;
  try {
    const res = await getAccountListApi({
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      ...formData.value,
    });
    dataList.value = res.data.pageList.records;
    totalCost.value = res.data.totalCost
    pagination.total = res.data.pageList.total;
    console.log(res, '业绩报表');
  } finally {
    getPerformanceListLoading = false;
    loading.value = false;
  }
}

// Debounced version for search input
function getPerformanceListDebounced() {
  if (getPerformanceListDebounceTimer)
    clearTimeout(getPerformanceListDebounceTimer);
  getPerformanceListDebounceTimer = setTimeout(() => {
    getPerformanceList();
  }, 300); // debounce delay (ms)
}

// Update handleSearch to use debounce
function handleSearch() {
  getPerformanceListDebounced();
}
function onChange(date: any, dateString: any) {
  console.log('Selected Time: ', date);
  console.log('Formatted Selected Time: ', dateString);
  getPerformanceListDebounced();
}
function onSelect(date: any, dateString: any) {
  console.log('Selected Time: ', date);
  console.log('Formatted Selected Time: ', dateString);
}

function handlePageChange(page) {
  pagination.currentPage = page;
  console.log('当前页码:', page); // 确认是否打印正确页码（如点击第2页应打印 2）
  getPerformanceList();
}
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  getPerformanceList();
}

onMounted(() => {
  getPerformanceList();
});
</script>

<style lang="scss" scoped>
@use '@/styles/var.scss' as *;
.filter-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
  width: 100%;
  align-items: start;
}

@media screen and (max-width: 768px) {
  .filter-box {
    grid-template-columns: 1fr;
  }
}

.filter-item {
  display: flex;
  align-items: start;
  flex-direction: column;
  gap: 8px;
  width: 100%;

  p {
    margin: 0;
    white-space: nowrap;
    flex-shrink: 0;
  }

  :is(.arco-picker, .arco-select, .arco-input-wrapper, .range-picker) {
    flex: 1;
    width: 100% !important;
  }

  @media screen and (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;

    p {
      margin-bottom: 6px;
    }
  }
}
</style>