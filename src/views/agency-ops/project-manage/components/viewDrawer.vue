<template>
    <div class="gi-page">
        <a-card>
            <a-drawer
                v-model:visible="visible"
                :title="title"
                :width="isMobile() ? '100%' : 700"
                :footer="false"
                @close="closed"
                :unmountOnClose="true"
            >
                <GiTable
                    :data="dataList"
                    :columns="columns"
                    :loading="loading"
                    :pagination="pagination"
                    @page-change="handlePageChange"
                    :scroll="{ x: '100%', y: '100%', minWidth: '100%'}"
                    @page-size-change="handlePageSizeChange"
                    :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']">
                </GiTable>
            </a-drawer>
        </a-card>
    </div>
</template>

<script lang="ts" setup>
    import { ref,  reactive, onMounted, watch } from 'vue';
    import { isMobile } from '@/utils';
    import { checkDetailApi } from '@/apis/agency-ops/project';
    import { Message } from '@arco-design/web-vue';
    defineOptions({ name: 'viewDrawer' });
    const visible = ref(false);
    const title = ref('查看');
    const loading = ref(false)

    const dataList = ref([])
    const columns = [
        { dataIndex: 'productName', title: '产品', align: 'center' },
        { dataIndex: 'industryName', title: '行业', align: 'center' },
        { dataIndex: 'ownerName', title: '负责人', align: 'center' },
        { dataIndex: 'createTime', title: '创建时间', align: 'center'},
    ]
    const pagination = reactive({
        currentPage: 1,           // 当前页
        pageSize: 10,         // 每页条数
        total: 0,             // 总数（在接口返回后更新）
        showTotal: true,      // 显示总数
        showPageSize: true,   // 显示每页条数选择器
    })
    const props = defineProps<{ id: string | number }>()
    const id = ref(props.id)
    // 监听父组件传来的 id 变化，变化时重新请求数据
    watch(() => props.id, (newId) => {
        id.value = newId
        if (newId) {
            checkDetail()
        }
    })
    function closed() {
        visible.value = false;
    }
    function open() {
        visible.value = true;
    }
    function handlePageChange(page){
        pagination.currentPage = page
    }
    function handlePageSizeChange(pageSize){
        pagination.pageSize = pageSize
    }
   function checkDetail() {
    loading.value = true;
    checkDetailApi(String(id.value), {
        page: pagination.currentPage,
        size: pagination.pageSize,
    }).then((res) => {
       if (res.code === "0") {
            dataList.value = res.data.list;
            pagination.total = res.data.total;
        } else {
            Message.error(res.msg || "获取客户列表失败");
            dataList.value = [];
            pagination.total = 0;
        }
    }).catch(() => {
            dataList.value = [];         // 网络或请求失败时清空数据
            pagination.total = 0;
            Message.error("获取客户列表失败");
        })
        .finally(() => {
            loading.value = false;
        });
    }


    onMounted(() => {
        if (id.value) {
            checkDetail()
        }
    })
    defineExpose({ open });
</script>