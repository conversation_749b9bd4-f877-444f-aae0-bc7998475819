<template>

  <div class="gi-page">

    <a-card class="gi-card-title">
      <a-drawer
        v-model:visible="visible"
        :title="title"
        :width="isMobile() ? '100%' : 600"
        :footer="false"
        @close="closePopup"
        :unmountOnClose="true"

      >

      <div class="record-drawer-content">
        <a-spin  :loading="loading" tip="等待授权..."   style="width: 100%;">
          <a-alert v-if="isHasProduct" style="margin-bottom: 20px;" type="warning" :banner="true">
                    暂未查询到当前主体下的产品，是否去创建
                  <template #action>
                    <a-button size="mini" type="text" @click="backPage">
                      <template #icon><icon-right/></template></a-button>
                  </template>
                </a-alert>
          <a-form :model="formData" :rules="rules" ref="formRef" layout="vertical" >
            <a-form-item field="customerId" label="客户" required>
              <a-spin :size="5"  :loading="loadingEntity"   style="width: 100%;">
              <a-select
                placeholder="请选择客户"
                v-model="formData.customerId"
                allow-clear
                allow-search
                @change="handleContractChange"
              >
                <a-option
                  v-for="item in generalStore.customerList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-option>
              </a-select>
              </a-spin>
            </a-form-item>

            <a-form-item  field="productId" label="产品" required>
              <div style="display: flex;flex-direction: column;width: 100%;">

                <a-select
                  :disabled="!productoOptions.length"
                  v-model="formData.productId"
                  placeholder="请选择"
                  :options="productoOptions"
                  allow-clear
                  size="large"
                />

              </div>


            </a-form-item>


            <a-form-item field="media" label="媒体" required>
              <a-select
                v-model="formData.media"
                placeholder="请选择媒体"
                allow-clear
                size="large"
              >
              <a-option
                v-for="item in mediaOptions"
                :key="item.id"
                :value="item.name"
              >
              {{ item.name }}
              </a-option>
            </a-select>
            </a-form-item>

            <a-form-item
              v-if="formData.media === '磁力引擎'"
              field="authType"
              label="授权类型"
              required
            >
              <a-select
                v-model="formData.authType"
                placeholder="选择授权类型"
                :options="authTypes"
                size="large"
              />
            </a-form-item>
             <a-form-item
              v-if="formData.media === '京准通'"
              field="authCookie"
              label="授权Cookie"
              required
            >
            <a-input v-model="formData.authCookie" placeholder="请输入授权Cookie" />
            </a-form-item>
            <a-form-item
              v-if="formData.media === '广点通'"
              field="authMethod"
              label="授权方式"
              required
            >
              <a-select
                v-model="formData.authMethod"
                placeholder="选择授权方式"
                :options="authMethods"
                size="large"
              />
            </a-form-item>
            <a-form-item
              v-if="formData.authMethod === '2'"
              field="authCookie"
              label="授权Cookie"
              required
            >
              <a-input v-model="formData.authCookie" placeholder="请输入授权Cookie" />
            </a-form-item>
          </a-form>

        <div style="margin-bottom: 20px;display: flex;flex-direction: row;align-items: center;justify-content: flex-end; ">
          <a-button type="primary" @click="submit" size="large" >授权</a-button>
        </div>
        </a-spin>
      </div>

      </a-drawer>
    </a-card>

  </div>

</template>
<script lang="ts" setup>
defineOptions({ name: 'AddDrawer' });
defineExpose({ open });
import { ref , reactive, shallowRef } from 'vue';
import { isMobile } from '@/utils';
import type { FormInstance  } from '@arco-design/web-vue';
import {sendAuthorized,checkAuthApi} from '@/apis/agency-ops/project'
import { Message } from '@arco-design/web-vue';
import {useUserStore} from '@/stores'
const userStore = useUserStore()
const loadingEntity= ref(false)
const loading = ref(false)
const visible = ref(false);
const title = ref('新增媒体账户');
const formRef = ref<FormInstance>();
const formData = ref({
  productId: '',
  media: '',
  customerId: '',
  authType: '',
  authCookie:'',
  authMethod:'',
  ownerUserId:userStore.userInfo.id
});
import { useGeneralDataStore } from '@/stores/generalData'
import { getProductApi } from '@/apis/general/general';
const generalStore = useGeneralDataStore()
const productoOptions = ref<any[]>([]);
const isHasProduct = ref(false)
const mediaOptions = computed(() =>
  generalStore.mediaList.filter((m) => m.visible),
);

// 媒体是磁力引擎时⬇️
const authTypes = ref([
  { label: '广告主', value: '广告主' },
  { label: '代理商', value: '代理商' },
])

// 媒体是广点通时⬇️
const authMethods = ref([
  { label: 'Api拉取授权', value: '1' },
  { label: 'Cookie授权', value: '2' },
])

const rules = {
  productId: [
    { required: true, message: '请选择产品', trigger: 'change' },
  ],
  media: [
    { required: true, message: '请选择媒体', trigger: 'change' },
  ],
  customerId: [
    { required: true, message: '请选择签约主体', trigger: 'change' },
  ],
  authType: [
    { required: true, message: '请选择授权类型', trigger: 'change' },
  ],
  authCookie: [
    { required: true, message: '请输入授权Cookie', trigger: 'change' },
  ],
  authMethod: [
    { required: true, message: '请选择授权方式', trigger: 'change' },
  ],
};

const pagination = reactive({
  current: 1, // 当前页
  pageSize: 8, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});

function open() {
  visible.value = true;
}
function closePopup() {
  visible.value = false;
  loading.value = false;
  if (authTimer) {
    clearTimeout(authTimer);
    authTimer = null;
  }
}

// 返回产品页面
const emit = defineEmits<{
  (e: 'switchTab', key: string): void
  (e: 'update:visible', val: boolean): void
  (e: 'update:authId', value: string[]): void
}>();
function backPage() {
  closePopup()
  formData.value = {
    productId: '',
    media: '',
    customerId: '',
    authType: '',
    ownerUserId:userStore.userInfo.id
  };
  setTimeout(() => {
      // 点击返回，切换到产品页
    emit('switchTab', '2');
    // 同时可以关闭抽屉
    emit('update:visible', false);
  }, 300);
}

function handleContractChange(selectedId) {
  const selectedItem = generalStore.customerList.find(item => item.id === selectedId)
  productoOptions.value = [];
  if (selectedItem) {
    let parm = {
      customerId: selectedId
    }
    loadingEntity.value = true
    getProductApi(parm)
      .then(res => {
        console.log(res, 'res')
        if (res.data.length > 0) {
          productoOptions.value = res.data.map(item => ({
            label: item.productName,
            value: item.id
          }))
          isHasProduct.value = false
          loadingEntity.value = false
          // Message.success('获取产品成功');
        } else {
          loadingEntity.value = false
          isHasProduct.value = true
        }
      })
      .catch(err => {
        console.error('Failed to get products:', err)
        productoOptions.value = [];
        loadingEntity.value = false
      });
  }
}


let uid = '';
let authTimer: ReturnType<typeof setTimeout> | null = null;

async function submit() {

  try {
    const valid = await formRef.value?.validate();
    if (!valid) {
      console.log('表单通过校验', formData.value);
      loading.value = true;
      const res = await sendAuthorized(formData.value)
      if (res.code == 0) {
        const toLink = res.data.authLink
        uid = res.data.uid
        // 京准通逻辑: 看 authCookieStatus
        if (formData.value.media === '京准通') {
          if (res.data.authCookieStatus === true) {
            Message.success(res.msg || '授权成功');
            loading.value = false;
            visible.value = false;
            formData.value = {
              productId: '',
              media: '',
              customerId: '',
              authType: '',
              authCookie: '',
              ownerUserId: userStore.userInfo.id
            };
            return true;
          } else {
            Message.warning('京准通授权未成功，请检查 Cookie');
            loading.value = false;
            return false;
          }
        }
        // 广点通逻辑
        if (formData.value.media === '广点通') {
          if (formData.value.authMethod === '2') {
            if (res.data.authCookieStatus === true) {
              Message.success(res.msg || '授权成功');
              loading.value = false;
              visible.value = false;
              formData.value = {
                productId: '',
                media: '',
                customerId: '',
                authType: '',
                authCookie: '',
                authMethod: '',
                ownerUserId: userStore.userInfo.id
              };
              return true;
            } else {
              Message.warning('广点通Cookie授权未成功，请检查 Cookie');
              loading.value = false;
              return false;
            }
          } else if (formData.value.authMethod === '1') {
            if (toLink) {
              window.open(toLink, '_blank');
              checkAuth();
              formData.value = {
                productId: '',
                media: '',
                customerId: '',
                authType: '',
                authCookie: '',
                authMethod: '',
                ownerUserId: userStore.userInfo.id
              };
              return true;
            } else {
              Message.warning('未获取到授权链接');
              loading.value = false;
              return false;
            }
          }
        }
        // 其他媒体逻辑: 跳转链接并轮询
        if (toLink) {
          window.open(toLink, '_blank');
          checkAuth();
          formData.value = {
            productId: '',
            media: '',
            customerId: '',
            authType: '',
            authCookie: '',
            ownerUserId: userStore.userInfo.id
          };
          return true;
        }

        Message.warning('未获取到授权链接');
        loading.value = false;
        return false;
      } else {
        Message.warning(res.msg || '请求失败')
        loading.value = false;
        return false
      }
    } else {
      Message.error('校验失败')
      return false;
    }
  } catch (e) {
    console.log('校验异常', e);
    loading.value = false;
    return false;
  }
}

import { useAuthStore } from '@/stores/authStore'
const authStore = useAuthStore()
let authId =  ref([''])
function checkAuth() {
  checkAuthApi(uid).then(res => {
    if (res.code === "0" && res.data?.isAuth === true) {
      loading.value = false
      visible.value = false
      const newAuthId = [res.data.authId]
      authId.value = newAuthId

      // 通过 Pinia 全局更新
      authStore.setAuthId(newAuthId)
      Message.success('授权成功')
      console.log(authId.value, 'authId.value')
    } else {
      authTimer = setTimeout(() => {
        checkAuth()
      }, 5000)
    }
  }).catch(() => {
    loading.value = false
  })
}
</script>
<style scoped>
.record-drawer-content{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}
  .red-dot {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: red;
    border-radius: 50%;
    margin-right: 4px;
  }
</style>
