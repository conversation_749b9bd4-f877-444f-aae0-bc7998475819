<template>
    <div class="gi_page">
        <a-card>
            <a-drawer
                v-model:visible="visible"
                :title="title"
                :width="isMobile() ? '100%' : 400"
                :footer="false"
                @close="closePopup"
                :unmountOnClose="true"
            >
                <div class="record-drawer-content">
                    <a-spin  :loading="loading" tip="保存中..."   >
                        <a-form :model="formData" :rules="rules" ref="formRef" layout="vertical">
                            <a-form-item field="productName" label="产品" required>
                                <a-input v-model="formData.productName" allow-clear placeholder="请输入产品名称" />
                            </a-form-item>
                            <a-form-item field="industryId" label="行业" required>
                                <a-select :style="{width:'320px'}" v-model="formData.industryId" placeholder="请选择行业名称" allow-clear allow-search>
                                    <a-option v-for="item in industryList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                                </a-select>
                            </a-form-item>
                            <a-form-item field="customerId" label="客户名称" required>
                                <a-select :style="{width:'320px'}" v-model="formData.customerId" placeholder="请选择客户名称" allow-clear allow-search>
                                    <a-option v-for="item in customerList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
                                </a-select>
                            </a-form-item>
                        </a-form>
                        <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-end; ">
                            <a-button type="primary" @click="submit" size="large" >保存</a-button>
                        </div>
                    </a-spin>
                </div>
            </a-drawer>
        </a-card>
    </div>
</template>

<script lang="ts" setup>
    defineOptions({ name: 'addProDrawer' });
import { ref } from 'vue';
    import { isMobile } from '@/utils';
    import type { FormInstance  } from '@arco-design/web-vue';
    import { getCustomerApi, getIndustryApi } from '@/apis/general/general';
    import { addProductApi } from '@/apis/agency-ops/project';
    import { Message } from '@arco-design/web-vue';
    const visible = ref(false);
    const title = ref('新增产品');
    const loading = ref(false)
    const formRef = ref<FormInstance>();
    const formData = ref({
        productName: '',
        industryId: '',
        customerId: '',
    });
    const props = defineProps<{
        industryList: IndustryItem[],
        customerList: CustomerItem[]
    }>();
    interface IndustryItem {
        id: string | number;
        name: string;
    }
    interface CustomerItem {
        id: string | number;
        name: string;
    }
    const rules = {
        productName: [
            { required: true, message: '请选择产品名称', trigger: 'change' },
        ],
        industryId: [
            { required: true, message: '请选择行业名称', trigger: 'change' },
        ],
        customerId: [
            { required: true, message: '请选择客户名称', trigger: 'change' },
        ],
    };
    const closePopup = () => {
        visible.value = false;
    };
    const open = () => {
        visible.value = true;
    };

    defineExpose({ open });

    const emit = defineEmits(['refresh']);

    async function submit() {
        // Validate the form first
        const valid = await formRef.value?.validate();
        if (valid) {
            // Validation failed, exit early
            console.log('校验失败');
            return;
        }
        loading.value = true;
        try {
            const res = await addProductApi(formData.value);
            if (res.code === '0') {
                Message.success('新增成功');
                visible.value = false;
                emit('refresh');
                formData.value = {
                    productName: '',
                    industryId: '',
                    customerId: '',
                };
            } else {
                Message.error(res.msg);
            }
        } catch (err) {
            Message.error('请求失败，请稍后重试');
        } finally {
            loading.value = false;
        }
        console.log('提交表单');
    }



</script>

<style>
    .record-drawer-content{
        display: flex;
        justify-content: center;
        align-content: center;
        width: 100%;
    }
</style>