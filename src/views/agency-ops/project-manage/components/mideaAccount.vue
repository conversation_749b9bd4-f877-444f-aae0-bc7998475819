<template>
  <div class="gi-page">
    <a-card class="gi-card-title">
      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="adAccountId"
        :row-selection="{ type: 'checkbox', showCheckedAll: true }"
        v-model:adAccountId="adAccountId"
        @select="onSelect"
        @select-all="onSelectAll"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #top>
          <mideaSearch
            :auth-id="authId"
            @search="handleSearch"
            @reset="handleReset"
          />
        </template>
        <template #toolbar-left>
          <a-button
            type="primary"
            @click="handleAdd"
            :style="{marginRight:isMobile()?'20px':'0px'} "
          >新增</a-button>
          <!-- <a-button
            @click="handlebBatchDelete"
            :disabled="isChoose"
            :loading="deleLoading"
            :style="{marginRight:isMobile()?'20px':'0px'} "
          >批量删除</a-button> -->
          <a-button
            @click="handleBatchEidt(selectRowList)"
            :disabled="isChoose"
            :loading="eidtLoading"
          >批量修改</a-button>
        </template>
        <template #toolbar-right>

          <!-- <a-button
            @click="handleBatchDeactivate"
            :style="{marginRight:isMobile()?'20px':'0px'} "
          >批量停用</a-button>
          <a-button @click="handleAccountDetail">账户详情</a-button> -->
        </template>
        <template #authStatus="{ record }">
          <a-tag
            :color="record.authStatus ==='1' ? 'green' : 'red'"
            size="medium"
          >
            {{ record.authStatus === '1' ? '已授权' : '未授权' }}
          </a-tag>
          <!-- <a-tag color="blue" size="medium" style="margin-left: 4px;">
                        {{ record.authType }}
                    </a-tag> -->
        </template>
        <!-- <template #validStatus="{ record }">
                      <a-tag
                      :color="record.validStatus === '1' ? 'green' : 'red'"
                      size="medium"
                      >
                      {{ record.validStatus === '1' ? '有效' : '无效' }}
                      </a-tag>
                  </template> -->
        <template #action="{ record }">
          <a-space>
            <!-- <a-button  type="text" size="mini" @click="handleEdit(record)">编辑</a-button> -->
            <!-- <a-button
              type="text"
              size="mini"
              @click="handleAuth(record)"
            >授权</a-button> -->
            <a-button
              type="text"
              size="mini"
              @click="handleRecord(record)"
            >记录</a-button>
            <!-- <a-button
              type="text"
              size="mini"
              @click="handleDelete(record)"
              status="danger"
            >删除</a-button> -->
          </a-space>
        </template>

      </GiTable>

    </a-card>
    <recordDrawer
      ref="recordDrawerRef"
      :id="id"
      @refreshParent="loadData(isSearch)"
    />
    <AddDrawer
      v-model:visible="drawerVisible"
      @switchTab="handleSwitchTab"
      ref="addDrawerRef"
    />
    <batchModiy
      ref="batchModiyRef"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue';
import { isMobile } from '@/utils';
import { getBusinessInfo, batchUpdateApi, exportApi } from '@/apis/agency-ops/project';
import { Message, Modal, Notification } from '@arco-design/web-vue';
import recordDrawer from './recordDrawer.vue';
import AddDrawer from './addDrawer.vue';
import batchModiy from './batchModiy.vue';
import MideaSearch from './mideaSearch.vue';
defineOptions({ name: 'mideaAccount' });

const emit = defineEmits<{
  (e: 'switchTab', key: string): void;
}>();
const drawerVisible = ref(false);
function handleSwitchTab(key: string) {
  // 向父组件触发事件
  emit('switchTab', key);
}

const isChoose = ref(true);
const deleLoading = ref(false);
const eidtLoading = ref(false);
const recordDrawerRef = ref();
const addDrawerRef = ref();
const batchModiyRef = ref();
const selectRowList = ref<{ adAccountId: string; media: string }[]>([]);
const id = ref('');
const authId = ref<string[]>([]);

const adAccountId = ref([]);

const columns = [
  { dataIndex: 'id', title: 'ID', width: 130, align: 'center' },
  { dataIndex: 'customerName', title: '客户', align: 'center', width: 150 },
  { dataIndex: 'productName', title: '产品', align: 'center', width: 150 },
  { dataIndex: 'media', title: '媒体', align: 'center', width: 150 },
  { dataIndex: 'authId', title: '授权ID', align: 'center', width: 170 },
  {
    dataIndex: 'authStatus',
    title: '授权状态',
    slotName: 'authStatus',
    align: 'center',
    width: 170,
  },
  {
    dataIndex: 'adAccountId',
    title: '广告账户ID',
    align: 'center',
    width: 130,
  },
  {
    dataIndex: 'adAccountName',
    title: '广告账户名称',
    align: 'center',
    width: 230,
  },
  // { dataIndex: 'validStatus', title: '有效状态' ,align:'center',slotName:'validStatus',width: 130},
  { dataIndex: 'ownerUserName', title: '运营', align: 'center', width: 130 },
  { dataIndex: 'createTime', title: '创建时间', align: 'center', width: 120 },
  {
    dataIndex: 'action',
    title: '操作',
    slotName: 'action',
    fixed: !isMobile() ? 'right' : undefined,
    align: 'center',
    width: 100,
  },
];
// 接口类型定义
interface AccountItem {
  id: number;
  productName: string;
  media: string;
  promoteEntityName: string;
  authStatus: string;
  authType: string;
  adAccountId: string;
  adAccountName: string;
  validStatus: number | string;
  ownerUserName: string;
  createTime: string;
}

const dataList = ref<AccountItem[]>([]);

// 分页类型
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});

// 加载状态
const loading = ref(false);

const searchForm = reactive({
  customerId: '',
  productId: '',
  media: '',
  ownerId: '',
  authId: '',
  adAccountId: '',
  adAccountName: '',
  validStatus: '',
  authStatus: '',
  createTime: '',
});

const isSearch = ref(0);

onMounted(() => {
  loadData(isSearch.value);
});

// 初始化列表
async function loadData(isSearch: number) {
  loading.value = true;
  if (isSearch === 1) {
    pagination.currentPage = 1;
  }
  try {
    // 过滤 searchForm 中为空的字段
    const params = Object.fromEntries(
      Object.entries(searchForm).filter(([_, v]) => {
        // 如果是数组，非空才保留；如果是字符串，非空才保留
        if (Array.isArray(v)) return v.length > 0;
        return v !== '' && v !== null && v !== undefined;
      }),
    );

    const res = await getBusinessInfo({
      page: pagination.currentPage,
      size: pagination.pageSize,
      ...params,
    });

    if (res.code === '0') {
      dataList.value = res.data.list;
      pagination.total = res.data.total;
    }
  } finally {
    loading.value = false;
  }
}

// 点击下一页事件
function handlePageChange(page) {
  pagination.currentPage = page;
  isSearch.value = 0;
  loadData(isSearch.value);
}

// 每页条数改变事件
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  loadData(isSearch.value);
}

function onSelect(selectedRowKeys: string[]) {
  // 更新 v-model
  adAccountId.value = selectedRowKeys;

  // 更新选中列表
  selectRowList.value = dataList.value
    .filter((item) => selectedRowKeys.includes(item.adAccountId))
    .map((item) => ({ adAccountId: item.adAccountId, media: item.media }));

  // 控制按钮状态
  isChoose.value = selectedRowKeys.length === 0;
}

function onSelectAll(selected: boolean) {
  if (selected) {
    // 全选
    adAccountId.value = dataList.value.map((item) => item.adAccountId);
    selectRowList.value = dataList.value.map((item) => ({
      adAccountId: item.adAccountId,
      media: item.media,
    }));
    isChoose.value = false;
  } else {
    // 取消全选
    adAccountId.value = [];
    selectRowList.value = [];
    isChoose.value = true;
  }
}

function handleSearch(params) {
  console.log('子组件回传的搜索参数:', params);
  isSearch.value = 1;
  // 直接把子组件参数全部赋值到 searchForm（后端需要的结构）
  Object.assign(searchForm, params);

  // 同步本地 authId（如果后续有地方要用）
  authId.value = params.authId || [];

  // 刷新列表
  loadData(isSearch.value);
}
function handleReset() {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = Array.isArray(searchForm[key]) ? [] : '';
  });
  authId.value = [];
  loadData(isSearch.value);
}

import { useAuthStore } from '@/stores/authStore';

const authStore = useAuthStore();

watch(
  () => authStore.authId,
  (val) => {
    console.log('Pinia authId 更新:', val);
    // 更新搜索条件
    searchForm.authId = val;
    authId.value = val; // 同步本地 ref
    loadData(isSearch.value); // 刷新列表
  },
  { immediate: true },
);

function handleAdd(record) {
  addDrawerRef.value?.open(record);
  // 新增逻辑
}
function handlebBatchDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `是否确认删除所选账户`,
    okText: '确认',
    cancelText: '取消',
    titleAlign: 'center',
    okLoading: deleLoading.value,
    bodyStyle: {
      fontSize: '14px',
      lineHeight: '1.6',
      textAlign: 'center',
    },
    onBeforeOk: async (done) => {
      try {
        deleLoading.value = true;
        console.log('确认删除账户：', adAccountId.value);

        // ✅ 删除接口
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // 删除成功后关闭弹窗
        done(true);


      } catch (e) {
        console.error('删除失败', e);
        done(false); // 保持弹窗打开
      } finally {
        deleLoading.value = false;
      }
    },
  });
}

function handleBatchEidt(selectedRowKeys) {
  batchModiyRef.value?.open(selectRowList.value);
  batchModiyRef.value.resetForm(); // 重置
  // 批量修改
}

function handleRefresh() {
  // 触发父组件刷新
  loadData(isSearch.value);
}

const btnLoading = ref(false);
 // 导出
async function handleExport() {
  btnLoading.value = true; // 开始 loading
  try {
    const res = await exportApi(searchForm);
    // 检查res.data是否为Blob，且类型正确
    if (
      !(res.data instanceof Blob) ||
      !res.data.type.includes('spreadsheetml')
    ) {
      throw new Error('API返回的数据不是有效的Excel文件');
    }

    const blob = res.data;
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // 从响应头解析文件名（处理URL编码和默认值）
    let filename = '报表.xlsx';
    if (res.headers?.['content-disposition']) {
      const match = res.headers['content-disposition'].match(/filename=(.+)/);
      if (match) filename = decodeURIComponent(match[1]);
    }
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败详情:', e);
    Message.error(`导出失败: ${e.message}`);
  } finally {
    btnLoading.value = false;
  }
}

function handleBatchDeactivate() {
  Notification.info({
    title: '提示',
    content: '功能正在开发中...',
  });
  // 批量停用
}
function handleAccountDetail() {
  Notification.info({
    title: '提示',
    content: '功能正在开发中...',
  });
  // 账户详情
}
function handleRecord(record) {
  id.value = record.id;
  console.log(id.value);
  recordDrawerRef.value?.open(record); // 触发抽屉打开
  // 记录逻辑
}
function handleEdit(record) {
  // 编辑逻辑
}
function handleDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `是否确认删除账户：${record.adAccountName}`,
    okText: '确认',
    cancelText: '取消',
    titleAlign: 'center',
    bodyStyle: {
      fontSize: '14px',
      lineHeight: '1.6',
      textAlign: 'center',
    },
    onOk: async () => {
      // 删除逻辑
      console.log('确认删除账户：', record);
      // await deleteAccount(record.id)
      // await loadData()
    },
  });
  // 删除逻辑
}

function handleAuth(record) {
  // 授权逻辑
}
// 动态筛选项宽度
const itemWidth = computed(() => (isMobile() ? '50%' : '25%'));
</script>

<style>
.expand-height-enter-active,
.expand-height-leave-active {
  transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.expand-height-enter-from,
.expand-height-leave-to {
  max-height: 0;
  opacity: 0;
}
.expand-height-enter-to,
.expand-height-leave-from {
  max-height: 1000px;
  opacity: 1;
}
</style>
