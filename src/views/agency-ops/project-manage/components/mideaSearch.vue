<template>
  <div class="view-box" >
    <div class="filter-box">
      <div style="width: 100%;">
        <p style="margin-bottom:10px;">客户</p>
        <!-- 客户 -->
        <a-select
          placeholder="请选择客户"
          v-model="province"
          :style="{width:'100%'}"
          size="medium"
          allow-clear
          allow-search
          @change="handleSelect"
          @clear="handleClear"
        >
          <a-option
            v-for="item in generalStore.customerList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          >
            <div class="ellipsis">
              <p class="ellipsis-item">{{item.name}}</p>
              <p style="font-size: 12px;color: #A6A9AF;line-height: 20px;">{{ item.businessLine }}|{{ item.saleName }}</p>
            </div>
          </a-option>
        </a-select>
      </div>
      <!-- 产品 -->
      <div style="width: 100%;">
        <p style="margin-bottom:10px;">产品</p>
          <a-select
          :disabled="!province"
          placeholder="请选择产品"
          v-model="productId"
          :style="{width:'100%'}"
          size="medium"
          allow-clear
          allow-search
          :max-tag-count="1"
          >
          <a-option
            v-for="item in industryList"
            :key="item.id"
            :value="item.id"
          >{{item.name}}</a-option>
        </a-select>
      </div>
      <!-- 媒体 -->
      <div style="width: 100%;">
        <p style="margin-bottom:10px;">媒体</p>
        <a-select
          :style="{width:'100%'}"
          v-model="media"
          placeholder="请输入媒体"
          allow-clear
          allow-search
          @clear="handleSearch"
        >
          <a-option
            v-for="item in mediaList"
            :key="item.id"
            :value="item.name"
          >{{item.name}}</a-option>
        </a-select>
      </div>
      <!-- 运营 -->
      <div  style="width: 100%;">
        <p style="margin-bottom:10px;">运营</p>
        <a-select
          :style="{width:'100%'}"
          v-model="ownerId"
          placeholder="请输入运营"
          allow-clear
          allow-search
          @clear="handleSearch"
        >

          <a-option
            v-for="item in generalStore.manageOperaList"
            :key="item.id"
            :value="item.id"
          >{{item.name}}</a-option>
        </a-select>
      </div>
      <!-- 账户 -->
      <div style="width: 100%;" v-show="isExpand"  >
        <p style="margin-bottom:10px;">账户</p>
        <div style="display: flex;flex-direction: row;align-content: center;justify-content: center;position: relative;" :style="{width:'100%'}">
            <a-select :style="{width:'35%'}" v-model="accountType" placeholder="请选择账户类型" :bordered="true">
                <a-option v-for="item in accountTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-option>
            </a-select>
            <a-input v-model="accountValue" placeholder="多个;分割"  allow-clear />
            <a-button style="width: 20%;" @click="toggleShow">{{ isShow ? '收起' : '展开' }}</a-button>
            <a-textarea
                ref="accountTextarea"
                v-model="accountValue"
                allow-clear
                style="
                    position: absolute;
                    left: 0; top: 40px;
                    width: 100%; height: 80px;
                    padding: 10px;"
                v-show="isShow"
                placeholder="请批量输入筛选内容，可换行/；分割"
                @blur="handleBlur"

            />
        </div>
      </div>
      <!-- 状态 -->
      <div style="width: 100%;" v-show="isExpand" >
        <p style="margin-bottom:10px;">状态</p>
        <a-select
          placeholder="状态"
          v-model="status"
          :style="{width:'100%'}"
          size="medium"
          allow-clear
          allow-search
          :max-tag-count="1"
          @clear="handleSearch"
        >
          <a-option
            v-for="item in statusList"
            :key="item.value"
            :value="item.value"
          >{{item.label}}</a-option>
          </a-select>
      </div>
      <!-- 创建时间 -->
      <div style="width: 100%;" v-show="isExpand" >
        <p style="margin-bottom:10px;">创建时间</p>
        <a-range-picker
          v-model="createTime"
          :style="{width:'100%'}"
          :time-picker-props="{
          defaultValue:['00:00:00','00:00:00']
          }"
          @change="onChange"
          @select="onSelect"
          @clear="handleSearch"
        />
      </div>
      <!-- 授权状态 -->
      <div style="width: 100%;" v-show="isExpand" >
        <p style="margin-bottom:10px;">授权状态</p>
        <a-select
          :style="{width:'100%'}"
          v-model="authStatus"
          placeholder="请选择授权状态"
          allow-clear
          @clear="handleSearch"
        >
          <a-option
            v-for="item in authStatusList"
            :key="item.value"
            :value="item.value"
          >{{item.label}}</a-option>
        </a-select>
      </div>
    </div>

    <div class="btn-box" >
      <a-button :disabled="!province && !productId && !media && !ownerId && !accountValue && !status && !createTime && !authStatus" type="primary" @click="handleSearch" style="margin-right: 10px;" >查询</a-button>
      <a-button @click="handleReset" style="margin-right: 10px;">重置</a-button>
      <a-button type="text" @click="isExpand = !isExpand">{{ isExpand ? '收起' : '展开' }}</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'MideaSearch' });
import { ref, computed, watch, nextTick, withDefaults } from 'vue';
import { isMobile } from '@/utils';
import {useGeneralDataStore} from '@/stores/generalData'
import { useAppStore } from '@/stores/useAppStore';
import { getProductApi } from '@/apis/general/general';
const generalStore = useGeneralDataStore()
const appStore = useAppStore()

const props = withDefaults(defineProps<{
  /** 父组件传入：授权ID数组，如 ['746385448309444642',''] */
  authId?: string[]
}>(), {
  authId: () => []
});

const statusList = ref([
  { label: '有效', value: 1 },
  { label: '无效  ', value: 2 },
])

const authStatusList = ref([
  { label: '已授权', value: 1 },
  { label: '授权失效', value: 2 },
])

const emit = defineEmits<{
  /** 子组件点击查询，将所有筛选参数一次性回传 */
  (e: 'search', payload: {
    province: string[];
    productId: (string | number);
    media?: string;
    ownerId?: string;
    accountType: number;
    accountList: string[];
    status?: string;
    createTime?: string;
    authStatus?: string;
    accountRaw: string;      // 原始输入串（调试/回显用）
  }): void;
  /** 子组件点击重置 */
  (e: 'reset'): void;
}>();

const isShow = ref(false);
// 用 computed 实现与 appStore.isExpand 的双向绑定
const isExpand = computed({
  get: () => {
    const fromStore = (appStore as any)?.isExpand
    if (typeof fromStore === 'boolean') return fromStore
    const saved = localStorage.getItem('isExpand')
    if (saved !== null) return saved === 'true'
    return true
  },
  set: (val: boolean) => {
    try {
      if ((appStore as any)) {
        (appStore as any).isExpand = val
      }
    } catch {}
    // 持久化
    localStorage.setItem('isExpand', String(val))
  }
})



const province = ref<string|number|undefined>('');
const productId = ref<string|number|undefined>('');
const media = ref<string | undefined>();
const ownerId = ref<string | undefined>();
const status = ref<string | undefined>();
const createTime = ref<string | undefined>();
const authStatus = ref<string | undefined>();

/** 账户输入：文本 + 数组 */
const accountValue = ref('');
const accountList = ref<string[]>([]);   // 解析后的数组（去空格去空项）

/** 接到父组件的 authId 时，自动回填到输入框，并同步数组 */
watch(() => props.authId, (val) => {
  const list = Array.isArray(val) ? val : [];
  accountList.value = list.filter(Boolean).map(s => String(s).trim());
  accountValue.value = accountList.value.join(';'); // 显示为分号拼接
}, { immediate: true });

/** 文本 -> 数组：监听输入框，随时解析 */
watch(accountValue, (val) => {
  accountList.value = String(val)
    .split(/[\n;；]+/)   // 换行 / 英文分号 / 中文分号 都能分割             // 按换行或分号分割
    .map(s => s.trim())
    .filter(Boolean);           // 去掉空字符串
});

/** 文本域显隐逻辑 */
const accountTextarea = ref<HTMLTextAreaElement | null>(null);
let blurTimeout: number | undefined;
watch(isShow, async (newVal) => {
  if (newVal) {
    await nextTick();
    accountTextarea.value?.focus();
  }
});
function handleBlur() {
  blurTimeout = window.setTimeout(() => {
    isShow.value = false;
  }, 100);
}
function toggleShow() {
  if (blurTimeout) {
    clearTimeout(blurTimeout);
    blurTimeout = undefined;
  }
  isShow.value = !isShow.value;
}
function onSelect(dateString, date) {
  console.log('onSelect', dateString, date);
}
function  onChange(dateString, date) {
  console.log('onChange: ', dateString, date);
}

const industryList = ref([]);

async function handleSelect(value) {
  const res = await getProductApi({
    customerId: value
  });

  // 映射接口数据到 industryList
  industryList.value = (res.data || []).map(item => ({
    id: item.id,
    name: item.productName
  }));

  console.log(industryList.value, '映射后的产品列表');
}
function handleClear(){
  industryList.value = []
  productId.value = ''
  handleSearch()
}
const itemWidth = computed(() => isMobile() ? '100%' : '25%');
const accountTypeOptions = [
  { label: '授权ID', value: 1 },
  { label: '账户名称', value: 2 },
  { label: '广告账户ID', value: 3 },
];
const accountType = ref(1);
// const mediaList = ref([
//   { label: '磁力引擎', value: '磁力引擎' ,index:1},
//   { label: '巨量千川', value: '巨量千川' ,index:2},
//   { label: '巨量广告', value: '巨量广告' ,index:3},
//   { label: '百度广告', value: '百度广告' ,index:4},
// ])
const mediaList = computed(() =>
  generalStore.mediaList.filter((m) => m.visible),
);
const handleSearch = () => {
  emit('search', {
    customerId: province.value,           // 客户ID
    productId: productId.value,                  // 产品ID
    media: media.value,                     // 媒体
    ownerId: ownerId.value,              // 运营名称
    authId: accountType.value === 1 ? accountList.value : [],          // 授权ID数组
    adAccountId: accountType.value === 3 ? accountList.value : [],     // 账户ID数组
    adAccountName: accountType.value === 2 ? accountList.value : [],   // 账户名称数组
    validStatus: status.value,              // 有效状态
    authStatus: authStatus.value,           // 授权状态
    createTime: createTime.value,          // 创建时间
  });
};

/** 重置：清空并通知父组件 */
const handleReset = () => {
  province.value = '';
  productId.value = '';
  media.value = undefined;
  ownerId.value = undefined;
  status.value = undefined;
  createTime.value = undefined;
  authStatus.value = undefined;
  accountType.value = 1;
  accountValue.value = '';
  accountList.value = [];
  emit('reset');
};
</script>

<style scoped>
.view-box{
  width: 100%;
}
.filter-box{
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}
@media screen and (max-width: 1200px) {
  .filter-box {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 648px) {
  .filter-box {
    grid-template-columns: 1fr;
  }
}

.btn-box{
  margin: 20px 0;
  text-align: end;
}
@media screen and (max-width: 648px) {
  .btn-box{
    text-align: start;
  }
}
.ellipsis{
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}


</style>