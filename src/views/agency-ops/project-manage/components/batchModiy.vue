<template>
  <div class="gi-page">
    <a-card>
      <a-drawer
        v-model:visible="visible"
        :title="title"
        :width="isMobile() ? '100%' : 500"
        :footer="false"
        :unmountOnClose="true"
        :mask-closable="true"
      >
        <div
          class="record-drawer-content"
          style="display: flex;flex-direction: column;"
        >
          <a-spin
            :loading="loading"
            tip="修改中..."
            style="width: 100%;"
          >
            <a-form
              :model="formData"
              :rules="rules"
              ref="formRef"
              layout="vertical"
            >
            <!-- 修改类型 -->
              <a-form-item
                field="typeId"
                label="修改类型"
                required
              >
                <a-select
                  v-model="formData.typeId"
                  placeholder="请选择修改类型"
                  allow-clear
                  allow-search
                  @change="handleInfoChange"
                >
                  <a-option
                    v-for="item in infoList"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>
              <!-- 客户 -->
              <a-form-item
                v-if="tempInfoId === 1 || tempInfoId === 2"
                field="customerId"
                label="客户"
                required
              >
                <a-select
                  v-model="formData.customerId"
                  placeholder="请选择客户"
                  @change="handleCustomer"
                  allow-search
                >
                  <a-option
                    v-for="item in generalStore.customerList"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>
              <!-- 产品 -->
              <a-form-item
                v-if="tempInfoId === 2"
                field="productId"
                label="产品"
                required
              >
                <a-select
                  v-model="formData.productId"
                  placeholder="请选择产品"
                  allow-clear
                  allow-search
                  :disabled="!productList.length"
                >
                  <a-option
                    v-for="item in productList"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.productName }}
                  </a-option>
                </a-select>
              </a-form-item>
              <!-- 运营 -->
              <a-form-item
                v-if="tempInfoId === 3"
                field="userId"
                label="运营"
                required
              >
                <a-select
                  v-model="formData.userId"
                  placeholder="请选择运营"
                  allow-clear
                  allow-search
                >
                  <a-option
                    v-for="item in generalStore.manageOperaList"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-form>

            <div style="margin-bottom: 20px;display: flex;flex-direction: row;align-items: center;justify-content: flex-end; ">
              <a-button
                type="primary"
                @click="submit"
                size="large"
              >保存</a-button>
            </div>
          </a-spin>
        </div>
      </a-drawer>
    </a-card>

  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'batchModiy' });
defineExpose({ open, resetForm });
import { onMounted } from 'vue';
import { isMobile } from '@/utils';
import { batchUpdateApi } from '@/apis/agency-ops/project';
import { getProductApi } from '@/apis/general/general';
import { Message } from '@arco-design/web-vue';
import { useGeneralDataStore } from '@/stores/generalData';
const generalStore = useGeneralDataStore();
const emit = defineEmits(['refresh']);
const visible = ref(false);
const title = ref('批量修改');
const loading = ref(false);
const infoList = ([
  // { id: 1, name: '客户' },
  { id: 2, name: '产品' },
  { id: 3, name: '运营' },
])
const formData = ref({
  userId: '',
  customerId: '',
  productId: '',
  adAccountList: [],
  typeId: '',
  updateType:2
});
const rules = {
  typeId: [{ required: true, message: '请选择修改类型', trigger: 'change' }],
  userId: [{ required: true, message: '请选择运营', trigger: 'change' }],
  customerId: [{ required: true, message: '请选择客户', trigger: 'change' }],
  productId: [{ required: true, message: '请选择产品', trigger: 'change' }],

};
const productList = ref([]);
onMounted(() => {
  generalStore.init()
});
const transferList = ref([]);
function open(selectedData) {
  formData.value.adAccountList = selectedData;
  console.log(selectedData, 'selectedData');
  visible.value = true;
}
const formRef = ref<FormInstance | null>(null);
function resetForm() {
  tempInfoId.value = null;
  formData.value = {
    ...formData.value,
    userId: '',
    customerId: '',
    productId: '',
    typeId: '',
    updateType:2
  };
  productList.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}
import type { FormInstance } from '@arco-design/web-vue';
const tempInfoId = ref<number | null>(null);
function handleInfoChange (){
  tempInfoId.value = Number(formData.value.typeId);
}
async function submit() {
  // 先校验
  try {
    await formRef.value?.validate();
  } catch (err) {
    Message.warning('请完整填写表单');
    return;
  }

  // 通过才提交
  loading.value = true;
  try {
    const res = await batchUpdateApi(formData.value);
    if (res.code === '0') {
      Message.success('修改成功');
      visible.value = false;
      emit('refresh');
      // 重置
      formData.value = {
        userId: '',
        customerId: '',
        productId: '',
        adAccountList: [],
        typeId: '',
      };
    } else {
      Message.error(res.msg || '修改失败');
    }
  } finally {
    loading.value = false;
  }
}
async function handleCustomer(record) {
  if(tempInfoId.value === 1){
    return
  }
  const res = await getProductApi({
    customerId: record,
  });
  if (res.data.length === 0) {
    Message.warning('该客户暂无产品');
  }
  productList.value = res.data;
}
</script>

<style>
</style>