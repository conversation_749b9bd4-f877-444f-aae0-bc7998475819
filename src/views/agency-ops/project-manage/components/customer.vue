<template>
  <div class="gi_page">
    <div class="gi_card">
      <a-card class="gi_card_title">
        <div class="content "  >
          <div class=" filter-box" style="">
            <div class="a-select-item">
              <p>客户</p>
              <a-input v-model="mainBody" placeholder="请输入"  allow-clear @clear="getSearchInfo" />
            </div>
            <div class="a-select-item" >
              <p >所属销售</p>
              <a-input v-model="sale" placeholder="请输入"  allow-clear @clear="getSearchInfo" />
            </div>
          </div>
          <div  class="" style="display: flex;flex-direction: row;justify-content: flex-start;">
            <a-button :disabled="!mainBody && !sale" type="primary" style="margin-right: 20px;" @click="getSearchInfo">查询</a-button>
          </div>
        </div>
      </a-card>
      <a-card class="gi_card_title" bordered="true" >
        <!-- 表格 -->
          <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="pagination"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']">
            <template #saleStatus="{ record }">
              <a-tag
                :color="record.status === '1' ? 'green' : 'red'"
                size="medium"
              >
                {{ record.status === '1' ? '合作中' : '停止合作' }}
              </a-tag>
            </template>


            <template #action="{ record }">
              <a-space>
                <a-button type="text" size="mini" @click="() => handleView(record)">查看</a-button>
              </a-space>
            </template>
          </GiTable>
      </a-card>
      <viewDrawer ref="viewDrawerRef" :id="currentId"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import { isMobile } from '@/utils'
import { throttle } from 'lodash'
defineOptions({ name: 'CustomerManage' });
import { ref, reactive, watch, onMounted } from 'vue';
import { getCustomerApi } from '@/apis/agency-ops/project'
import { Message } from '@arco-design/web-vue';
import viewDrawer from './viewDrawer.vue'

const viewDrawerRef = ref();
const mainBody= ref('');
const sale = ref('');
const currentId = ref('')
onMounted(()=>{
  getCustomer()
})

const loading = ref(false)
const selectedKeys = ref([])
const pagination = reactive({
  currentPage: 1,           // 当前页
  pageSize: 10,         // 每页条数
  total: 0,             // 总数（在接口返回后更新）
  showTotal: true,      // 显示总数
  showPageSize: true,   // 显示每页条数选择器
  showJumper: true      // 显示跳转页码输入框
})

const columns = [
  { dataIndex: 'contractEntityName', title: '客户',   align: 'center' },
  { dataIndex: 'businessLine', title: '业务线', align: 'center' },
  { dataIndex: 'saleName', title: '所属销售', align: 'center' },
  { dataIndex: 'operatorNames', title: '所属运营', align: 'center' },
  { dataIndex: 'status', title: '客户状态', slotName: 'saleStatus', align: 'center' },
  { dataIndex: 'action', title: '操作', slotName: 'action', fixed: !isMobile() ? 'right' : undefined, align: 'center',width:100 }
]

const dataList = ref()

async function getCustomer() {
  loading.value = true
  try {
    const res = await getCustomerApi({
      page: pagination.currentPage,
      size: pagination.pageSize,
    })
    console.log(res.code,'code')
    if (res.code === "0") {
      dataList.value = res.data.list;
      pagination.total = res.data.total;
    } else {
      Message.error(res.msg || '获取客户列表失败');
    }
  } finally {
    loading.value = false
  }
}
function handlePageChange(page){
  pagination.currentPage = page
  getCustomer()
}
function handlePageSizeChange(pageSize){
  pagination.pageSize = pageSize
  getCustomer()
}

function getSearchInfo() {
  getCustomerApi({
    page: pagination.currentPage,
    size: pagination.pageSize,
    contractEntityName: mainBody.value || '',
    saleName: sale.value || '',
  }).then((res) => {
    loading.value = true;
    if (res.code === "0") {
      dataList.value = res.data.list;
      pagination.total = res.data.total;
    } else {
      Message.error(res.msg || '获取客户列表失败');
    }
  }).finally(() => {
    loading.value = false;
  });
}

// 节流请求
const throttledSearch = throttle(getSearchInfo, 500);

function handleView(record){
  currentId.value = record.id
  viewDrawerRef.value?.open()
  console.log('查看')
}
</script>
<style scoped lang="scss">
@use '@/styles/var.scss' as *;

.content {
  display: grid;
  grid-template-columns: auto 600px;
  gap: 20px;
    @media (max-width:1200px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.page-title {
  color: var(--color-text-1);
  margin-bottom: $margin;
}

.description {
  color: var(--color-text-2);
  margin-bottom: $margin;
}

.filter-box {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 90%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  @media (max-width: 1200px) {
    width: 100%;
    grid-template-columns: repeat(1, 1fr);
  }
}
.a-select-item{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 400px;
    >p{
      width: 100px;
    }
    @media screen and (max-width: 1200px) {
      flex-direction: column;
      align-items:flex-start;
      width: 100%;
      >p{
      margin:0 0 10px 0;
    }
    }
}
</style>
