<template>
  <div class="gi_page">
    <a-card class="gi_card_title">
      <div class="content ">
        <div class=" filter-box">
          <div class="a-select-item">
            <p>客户</p>
            <a-select
              placeholder="请选择"
              v-model="province"
              allow-clear
              allow-search
              @change="handleSelect"
              @clear="handleCustomerReset"
            >
              <a-option
                v-for="item in generalStore.customerList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              >{{ item.name }}</a-option>
            </a-select>
          </div>

          <div class="a-select-item">
            <p>产品</p>
            <a-select
              :disabled="!province"
              placeholder="请选择产品"
              v-model="productId"
              size="medium"
              allow-clear
              allow-search
              :max-tag-count="1"
            >
              <a-option
                v-for="item in productList"
                :key="item.id"
                :value="item.id"
              >{{item.name}}</a-option>
            </a-select>
          </div>
          <div class="a-select-item">
            <p>状态</p>
            <a-select
              v-model="status"
              placeholder="请选择状态"
              allow-clear
              @clear="getSearchInfo"
            >
              <a-option
                v-for="item in statusList"
                :key="item.value"
                :value="item.value"
              >{{ item.label }}</a-option>
            </a-select>
          </div>
          <div class="a-select-item">
            <p>行业</p>
            <a-select
              v-model="industryId"
              placeholder="请选择行业名称"
              allow-clear
              allow-search
              @clear="getSearchInfo"
            >
              <a-option
                v-for="item in generalStore.industryList"
                :key="item.id"
                :value="item.id"
              >{{ item.name }}</a-option>
            </a-select>
          </div>
        </div>
        <div class="btn-box">
          <a-button
            type="primary"
            style="margin-right: 20px;"
            @click="getSearchInfo"
            :disabled="!province && !industryId && !productId && !status"
          >查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </div>
      </div>
    </a-card>
    <a-card
      class="gi_card_title"
      bordered="true"
    >
      <a-scrollbar style="overflow: auto;">
        <a-space>
          <a-button
            type="primary"
            @click="handleAdd"
          >
            <template #icon>
              <icon-plus />
            </template>
            <!-- Use the default slot to avoid extra spaces -->
            <template #default>新增</template>
          </a-button>
          <!-- <a-button type="outline" status="danger" disabled>
              <template #icon>
                <icon-delete />
              </template>
              <template #default>批量删除</template>
            </a-button>
            <a-button type="outline"  disabled>
              <template #icon>
                <icon-lock />
              </template>
              <template #default>批量停用</template>
            </a-button>
            <a-button type="outline" disabled>
              <template #icon>
                <icon-unlock />
              </template>
              <template #default>批量启用</template>
            </a-button>
            <div style="width: 200px;">
              <a-input placeholder="添加负责人" ></a-input>
            </div> -->

        </a-space>
      </a-scrollbar>

      <!-- 表格 -->

      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #status="{ record }">
          <a-tag
            :color="record.status === 1 ? 'green' : 'red'"
            size="medium"
          >
            {{ record.status === 1 ? '启用' : '未启用' }}
          </a-tag>
        </template>

        <!-- <template #action="{ record }">
              <a-space>
                <a-button type="text" size="mini" @click="() => handleEdit(record)">编辑</a-button>
                <a-button type="text" size="mini" @click="() => handleCopy(record)">复制</a-button>
                <a-button type="text" size="mini" status="danger" @click="() => handleDelete(record)">删除</a-button>
              </a-space>
            </template> -->
      </GiTable>
      <addProDrawer
        ref="addProDrawerRef"
        @refresh="getProduct"
        :customer-list="generalStore.customerList"
        :industry-list="generalStore.industryList"
      />
    </a-card>

  </div>
</template>

<script setup lang="ts">
import { isMobile } from '@/utils';
defineOptions({ name: 'ProductManage' });
import { ref, reactive, onMounted } from 'vue';
import { getProductApi } from '@/apis/agency-ops/project';
import { Message } from '@arco-design/web-vue';
import addProDrawer from './addProDrawer.vue';
const addProDrawerRef = ref();
const province = ref('');
const industryId = ref('');
const productId = ref('');

import { useGeneralDataStore } from '@/stores/generalData';
const generalStore = useGeneralDataStore();
onMounted(() => {
  getProduct();
  if (!generalStore.customerList.length) {
    generalStore.fetchCustomerList();
  }
  if (!generalStore.industryList.length) {
    generalStore.fetchIndustryList();
  }
});
const loading = ref(false);
const selectedKeys = ref([]);
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});

const columns = [
  {
    dataIndex: 'productName',
    title: '产品',
    width: 130,
    fixed: !isMobile() ? 'left' : undefined,
    align: 'center',
  },
  { dataIndex: 'industryName', title: '行业', align: 'center', width: 150 },
  { dataIndex: 'customerName', title: '客户名称', align: 'center', width: 150 },
  {
    dataIndex: 'createTime',
    title: '创建时间',
    slotName: 'authStatus',
    align: 'center',
    width: 170,
  },
  {
    dataIndex: 'status',
    title: '状态',
    slotName: 'status',
    align: 'center',
    width: 230,
  },
  // { dataIndex: 'action', title: '操作', slotName: 'action', fixed: !isMobile() ? 'right' : undefined, align: 'center', width: 150 }
];

const status = ref('');

const statusList = ref([
  { value: '1', label: '启用' },
  { value: '2', label: '未启用' },
]);
const productList = ref([]);
async function handleSelect(key){
  const res = await getProductApi({
    customerId: key
  });
  console.log(res.data, '产品');

  // 映射接口数据到 industryList
  productList.value = (res.data.list || []).map(item => ({
    id: item.id,
    name: item.productName
  }));
}

function getSearchInfo() {
  getProductApi({
    page: pagination.currentPage,
    size: pagination.pageSize,
    industryId: industryId.value || '',
    productId: productId.value || '',
    customerName: province.value || '',
    status: status.value || '',
  })
    .then((res) => {
      loading.value = true;
      if (res.code === '0') {
        if (res.data.total === 0) {
          Message.warning('没有找到匹配的数据');
        }
        dataList.value = res.data.list;
        pagination.total = res.data.total;
      } else {
        Message.error(res.msg || '获取客户列表失败');
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleCustomerReset() {
  industryId.value = '';
  productId.value = '';
  productList.value = [];
  getSearchInfo();
}

function handleReset() {
  province.value = '';
  industryId.value = '';
  productId.value = '';
  status.value = '';
  productList.value = [];
  getSearchInfo();
}

function handleAdd() {
  addProDrawerRef.value?.open();
}

const dataList = ref();

function handleEdit(record) {
  console.log('编辑', record);
}

function handleCopy(record) {
  console.log('复制', record);
}

function handleDelete(record) {
  console.log('删除', record);
}

async function getProduct() {
  loading.value = true;
  try {
    const res = await getProductApi({
      page: pagination.currentPage,
      size: pagination.pageSize,
    });
    if (res.code === '0') {
      dataList.value = res.data.list;
      pagination.total = res.data.total;
    } else {
      Message.error(res.msg || '获取客户列表失败');
    }
  } finally {
    loading.value = false;
  }
}
function handlePageChange(page) {
  pagination.currentPage = page;
  getProduct();
}
function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  getProduct();
}
</script>
<style scoped lang="scss">
@use '@/styles/var.scss' as *;

.content {
  display: grid;
  grid-template-columns: auto 200px;
  gap: 20px;
  width: 100%;
  @media (max-width: 1700px) {
    grid-template-columns: repeat(1, 1fr);
  }
}

.page-title {
  color: var(--color-text-1);
  margin-bottom: $margin;
}

.description {
  color: var(--color-text-2);
  margin-bottom: $margin;
}

.btn-box {
  text-align: end;
  @media screen and (max-width: 648px) {
    text-align: start;
  }
}

.filter-box {
  gap: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  @media (max-width: 1200px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 648px) {
    width: 100%;
    grid-template-columns: repeat(1, 1fr);
  }
}
.a-select-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 300px;
  > p {
    width: 50px;
  }
  @media screen and (max-width: 1700px) {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    > p {
      margin: 0 0 10px 0;
    }
  }
}
</style>
