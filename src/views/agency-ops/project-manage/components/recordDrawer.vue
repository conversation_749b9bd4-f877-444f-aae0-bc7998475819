<template>
  <div class="gi-page">
    <a-card>

      <a-drawer
        v-model:visible="visible"
        :title="title"
        :width="isMobile() ? '100%' : 700"
        :footer="false"
        @close="closed"
        :unmountOnClose="true"
      >
        <div class="record-drawer-content">
          <header class="record-table-header">
            <span>信息</span>
          </header>
          <div class="record-info">
            <a-popover>
              <div
                class="info-item"
                v-if="recordData?.productName"
              >产品名称：{{ recordData?.productName }}</div>
              <div
                class="info-item"
                v-else
              >产品名称：\</div>
              <template #content>
                <p v-if="recordData?.productName">{{ recordData?.productName }}</p>
                <p v-else>\</p>
              </template>
            </a-popover>
            <a-popover>
              <div
                class="info-item"
                v-if="recordData?.adAccountName"
              >账户名称：{{ recordData?.adAccountName }}</div>
              <div
                class="info-item"
                v-else
              >账户名称：\</div>
              <template #content>
                <p v-if="recordData?.adAccountName">{{ recordData?.adAccountName }}</p>
                <p v-else>\</p>
              </template>
            </a-popover>
            <a-popover>
              <div
                class="info-item"
                v-if="recordData?.media"
              >媒体：{{ recordData?.media }}</div>
              <div
                class="info-item"
                v-else
              >账户名称：\</div>
              <template #content>
                <p v-if="recordData?.media">{{ recordData?.media }}</p>
                <p v-else>\</p>
              </template>
            </a-popover>
          </div>
          <!-- <a-card class="record-info">

                    </a-card> -->
          <div>
            <header class="record-table-header">
              <span>运营</span>
              <a-button
                type="primary"
                size="mini"
                @click="handleNestedClick"
              >添加</a-button>
            </header>
            <GiTable
              :data="dataList"
              :columns="columns"
              :loading="loading"
              :pagination="pagination"
              :scroll="{ x: 'max-content' }"
              :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
            >
              <template #action="{ record }">
                <a-space>
                  <a-button
                    type="text"
                    size="mini"
                    @click="handleDelete(record)"
                    status="danger"
                  >删除</a-button>
                  <!-- <a-button
                    type="text"
                    size="mini"
                    @click="handleEdit(record)"
                  >编辑</a-button> -->
                </a-space>
              </template>
            </GiTable>
          </div>
        </div>
        <!--  -->
        <a-drawer
          :visible="nestedVisible"
          :width="isMobile() ? '100%' : 500"
          @cancel="handleNestedCancel"
          @ok="handleOnOk"
          @BeforeClose="beForNestedCancel"
          unmountOnClose
        >
          <template #title>
            添加记录
          </template>
          <div style="padding: 16px;">
            <a-form
              :model="formResult"
              :rules="rules"
              layout="vertical"
              ref="formRef"
            >
              <a-form-item
                field="startDate"
                label="日期  "
                required
                prop="startDate"
              >
                <a-range-picker
                  style="width: 100%;"
                  :placeholder="['开始日期', '结束日期']"
                  :disabled-date="isDisabledDate"
                  value-format="YYYY-MM-DD"
                  allow-clear
                  show-confirm-btn
                  @change="onRangeChange"
                />
              </a-form-item>
              <a-form-item
                field="userId"
                label="运营"
                required
                prop="userId"
              >
                <a-select
                  :style="{width:'100%'}"
                  placeholder="请选择运营"
                  @change="handleSelectChange"
                  v-model="formResult.userId"
                >
                  <a-option
                    v-for="item in generalStore.manageOperaList"
                    :key="item.id"
                    :value="item.id"
                  >{{item.name}}</a-option>
                </a-select>
              </a-form-item>

            </a-form>

          </div>
        </a-drawer>
      </a-drawer>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import type { FormInstance  } from '@arco-design/web-vue';
defineOptions({ name: 'recordDrawer' });
const props = defineProps<{
  id?: string;
  adAccountId?: string;
}>();
const visible = ref(false);
const title = ref('记录');
const recordData = ref<any>(null);
const generalStore = useGeneralDataStore();
const formRef = ref<FormInstance | null>(null);

import { useGeneralDataStore } from '@/stores/generalData';
import { isMobile } from '@/utils';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { batchUpdateApi, deleteRecordApi, getRecordList } from '@/apis/agency-ops/project';
import { Message } from '@arco-design/web-vue';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

const rules = {
  startDate: [{ required: true, message: '请选择日期', trigger: 'change' }],
  userId: [{ required: true, message: '请选择运营', trigger: 'change' }],
};

const dataList = ref<any>([]);

const loading = ref(false);
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const columns = [
  {
    dataIndex: 'startDate',
    title: '起始日期',
    width: 100,
    fixed: !isMobile() ? 'left' : undefined,
    align: 'center',
  },
  {
    dataIndex: 'endDate',
    title: '结束日期',
    width: 100,
    fixed: !isMobile() ? 'left' : undefined,
    align: 'center',
  },
  { dataIndex: 'ownerUserName', title: '运营', width: 150, align: 'center' },
  {
    dataIndex: 'action',
    title: '操作',
    width: 150,
    align: 'center',
    slotName: 'action',
  },
];

const nestedVisible = ref(false);

const selectedRange = ref<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
const selectedOperator = ref('');

// 用于存储选中值（开始日期、结束日期、运营）
const formResult = reactive({
  updateType:1,
  startDate: '',
  endDate: '',
  userId: '',
  typeId:3,
  adAccountList: [
    {
      adAccountId: '',
      media: '',
    },
  ],
});
// 控制禁用的时间段，格式为 [开始日期, 结束日期]
const disabledDates = ref<[string, string][]>([]);

function isDisabledDate(current: Date) {
  if (!disabledDates.value.length) return false;
  const currentDay = dayjs(current).format('YYYY-MM-DD');

  return disabledDates.value.some(([start, end]) =>
    dayjs(currentDay).isSameOrAfter(start) &&
    dayjs(currentDay).isSameOrBefore(end)
  );
}

function onRangeChange(value: [string, string] | undefined) {
  if (value && value.length === 2) {
    formResult.startDate = value[0];
    formResult.endDate = value[1];
  } else {
    formResult.startDate = '';
    formResult.endDate = '';
  }
}

function handleSelectChange(value: string) {
  formResult.userId = value;
  console.log(formResult);
}

function handleNestedClick() {
  if (recordData.value) {
    formResult.adAccountList = [{
      adAccountId: recordData.value.adAccountId || '',
      media: recordData.value.media || ''
    }];
  }
  nestedVisible.value = true;
}

function beForNestedCancel() {
  // 关闭前
  formResult.startDate = '';
  formResult.endDate = '';
  formResult.userId = '';
  selectedRange.value = null;
  selectedOperator.value = '';
}
const emit = defineEmits<{
  (e: 'refreshParent'): void;
}>();



async function fetchDataList(adAccountId: string) {
  if (!adAccountId) {
    dataList.value = [];
    pagination.total = 0;
    return;
  }
  loading.value = true;
  dataList.value = [];
  try {
    const res = await getRecordList(adAccountId, {
      page: pagination.currentPage,
      size: pagination.pageSize,
    });
    if (res.code === '0') {
      dataList.value = res.data.list;
      pagination.total = res.data.total;

      // 把每条记录的 startDate / endDate 收集成禁用区间
      disabledDates.value = res.data.list
        .filter((item: any) => item.startDate && item.endDate)
        .map((item: any) => [item.startDate, item.endDate]);
    }
  } finally {
    loading.value = false;
  }
}

watch(() => props.adAccountId, (newVal) => {
  fetchDataList(newVal || '');
}, { immediate: true });

async function handleOnOk() {
  if (!formRef.value) {
    Message.error('表单未初始化');
    return;
  }

  try {
    await formRef.value.validate(); // 必须调用 validate()
  } catch (err) {
    console.log('校验失败', err);
    Message.error('请完善必填项或检查填写格式');
    return; // 阻止后续请求
  }
  // 校验通过，执行接口
  try {
    const res = await batchUpdateApi(formResult);
    if (res.code === '0') {
      emit('refreshParent');
      nestedVisible.value = false;

      setTimeout(() => {
        visible.value = false;
        formResult.startDate = '';
        formResult.endDate = '';
        formResult.userId = '';
        formResult.adAccountList = [{ adAccountId: '', media: '' }];
        Message.success('添加成功');
      }, 300);
    } else {
      Message.error(res.msg || '添加失败，请稍后重试');
    }
  } catch (error) {
    console.error('接口请求异常', error);
  }
}

function handleNestedCancel() {
  nestedVisible.value = false;
}

async function open(data?: any) {
  recordData.value = data;
  visible.value = true;
  // If adAccountId exists in the provided data, fetch data list
  console.log(data,'数据')
  if (data && data.adAccountId) {
    loading.value = true;
    dataList.value = [];
    await fetchDataList(data.adAccountId);
  }
}

function closed() {
  recordData.value = null;
  visible.value = false;
}

async function handleDelete(record) {
  try {
    console.log('Deleting record:', record);
    const res = await deleteRecordApi(record.id);
    if (res.code === '0') {
      Message.success('删除成功');
      // 刷新当前列表数据
      if (recordData.value && recordData.value.adAccountId) {
        await fetchDataList(recordData.value.adAccountId);
      }
    } else {
      Message.error(res.msg || '删除失败，请稍后重试');
    }
  } catch (error) {
    console.error('删除接口请求异常', error);
    Message.error('删除失败，请稍后重试');
  }
}

// function handleEdit(record) {
//   // 编辑逻辑
// }

function range(start: number, end: number): number[] {
  const result: number[] = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

defineExpose({ open });
</script>

<style scoped>
.record-drawer-content {
  /* padding: 16px; */
  font-size: 14px;
  display: flex;
  flex-direction: column;
}
.record-info {
  /* flex-wrap: wrap; */
  /* gap: 12px; */
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  /* border: 1px solid #e5e5e5; */
  margin-bottom: 20px;
  /* padding: 5px; */
}

.info-item {
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  max-width: 180px;
}

@media (max-width: 768px) {
  .record-info {
    display: flex;
    flex-direction: column;
  }
  .info-item {
    font-size: 14px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    max-width: 100%;
    margin-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
    line-height: 30px;
  }
}

.record-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  font-size: 15px;
}

@media (max-width: 768px) {
  .record-table-header {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }
}

/* ::v-deep(.record-drawer-content  .arco-trigger-popup  .arco-trigger-position-bl) {
  scale: 0.9;
  width: 100vw;
  max-width: 100vw;
} */
</style>
