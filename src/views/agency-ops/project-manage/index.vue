<template>
  <div class="gi_page">
    <a-tabs size="large" v-model:active-key="activeTab">
      <a-tab-pane key="1" title="客户">
        <Customer />
      </a-tab-pane>
      <a-tab-pane key="2" title="产品">
        <Product/>
      </a-tab-pane>
      <a-tab-pane key="3" title="媒体账户">
        <mideaAccount @switchTab="handleSwitchTab" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Customer from './components/customer.vue';
import mideaAccount  from './components/mideaAccount.vue';
import Product from './components/product.vue';
defineOptions({ name: 'MyNewPage' });
const activeTab = ref('1');
function handleSwitchTab(key: string) {
  activeTab.value = key; // 切换 Tab
}
</script>

<style scoped lang="scss">
@use '@/styles/var.scss' as *;

.content {
  padding: $padding;
}

.page-title {
  color: var(--color-text-1);
  margin-bottom: $margin;
}

.description {
  color: var(--color-text-2);
  margin-bottom: $margin;
}
</style>