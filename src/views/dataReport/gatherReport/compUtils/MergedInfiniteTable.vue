<template>
  <!-- @scroll="handleScroll"
    :style="{ height: tableHeight+'px' }" -->
  <div
    class="table-scroll-container"
    ref="scrollContainer"
  >
    <a-table
      :hoverable="false"
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :span-method="spanMethod"
      :bordered="{ wrapper: true, cell: true }"
      :pagination="false"
      :scroll="{ x: '100%',y: tableHeight }"
      :loading="loading"
      @scroll="handleScroll"
      :row-class="rowClass"
      @cell-mouse-enter="handleRowEnter"
      @cell-mouse-leave="handleRowLeave"
    >
      <template #cell-customerCost="{ record }">
        {{ formatNumber(record.customerCost) }}
      </template>
      <template #cell-operationCost="{ record }">
        {{ formatNumber(record.operationCost) }}
      </template>
      <template #cell-designCost="{ record }">
        {{ formatNumber(record.designCost) }}
      </template>

    </a-table>

    <!-- 加载提示 -->
    <!-- <div v-if="loading" class="loading-tip">加载中...</div>
    <div v-if="!hasMore && tableData.length > 0" class="no-more-tip">没有更多数据了</div> -->
  </div>
</template>

<script setup lang="ts">
const hoveredCustomerId = ref<string | number | null>(null);
// 给行加 class
const rowClass = (record: TableRow) => {
  if (record.customerId === hoveredCustomerId.value) {
    return 'highlight-row';
  }
  return '';
};
// 监听行的 mouseenter / mouseleave
function handleRowEnter(record: TableRow) {
  console.log('hoveredCustomerId', record.customerId);
  hoveredCustomerId.value = record.customerId;
}
function handleRowLeave() {
  hoveredCustomerId.value = null;
}
import { defineProps, defineEmits } from 'vue';
import { getGatherDataListApi } from '@/apis/dataReport';
import {
  ref,
  watch,
  onMounted,
  defineExpose,
  computed,
  onBeforeUnmount,
} from 'vue';
import { formatNumber } from '@/utils/formatNumber';
const props = defineProps<{
  tableHeight?: string | number;
  formData?: Record<string, any>;
  active?: string | number | boolean;
}>();
const emit = defineEmits(['reachBottom']);
const loading = ref(false);
const tableRef = ref();

// 用 computed 代理 props，保持响应式
const tableHeight = computed(() => props.tableHeight ?? '400');

const columns = ref([
  {
    title: '客户',
    dataIndex: 'customerName',
    width: 130,
    align: 'left',
    fixed: 'left',
  },
  {
    title: '消耗汇总',
    dataIndex: 'customerCost',
    align: 'center',
    width: 100,
    slotName: 'cell-customerCost',
  },
  {
    title: '优化',
    dataIndex: 'operationName',
    align: 'center',
    width: 80,

  },
  {
    title: '优化消耗',
    dataIndex: 'operationCost',
    align: 'center',
    width: 100,
    slotName: 'cell-operationCost',
  },
  {
    title: '设计',
    dataIndex: 'designName',
    align: 'center',
    width: 80,
  },
  {
    title: '设计消耗',
    dataIndex: 'designCost',
    align: 'center',
    width: 100,
    slotName: 'cell-designCost',
  },
]);

watch(
  () => props.active,
  (newVal) => {
    if (newVal) {
      const hasDateColumn = columns.value.some(col => col.dataIndex === 'activeDate');
      if (!hasDateColumn) {
        columns.value.push({
          title: '日期',
          dataIndex: 'activeDate',
          align: 'center',
          width: 120,
        });
      }
    }
  },
  { immediate: true }
);

interface TableRow {
  customerId: string | number;
  customerName: string;
  customerCost: number;
  operationName: string;
  operationCost: number;
  designName: string;
  designCost: number;
  operation?: string;
}
const tableData = ref<TableRow[]>([]);

const scrollContainer = ref<HTMLElement | null>(null);

const spanMethod = ({ rowIndex, columnIndex, record }) => {
  // 客户 / 消耗汇总 列（第 0、1 列）
  if ([0, 1].includes(columnIndex)) {
    const customerId = record.customerId;
    const sameCustomerRows = tableData.value.filter(
      (item) => item.customerId === customerId,
    );
    const firstRowIndex = tableData.value.findIndex(
      (item) => item.customerId === customerId,
    );
    const rowCount = sameCustomerRows.length;

    if (rowIndex === firstRowIndex) {
      return { rowspan: rowCount, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  // 运营 / 运营消耗 列（第 2、3 列）
  if ([2, 3].includes(columnIndex)) {
    const customerId = record.customerId;
    const operation = record.operationName;
    const sameOperationRows = tableData.value.filter(
      (item) => item.customerId === customerId && item.operationName === operation,
    );
    const firstRowIndex = tableData.value.findIndex(
      (item) => item.customerId === customerId && item.operationName === operation,
    );
    const rowCount = sameOperationRows.length;

    if (rowIndex === firstRowIndex) {
      return { rowspan: rowCount, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  // 设计 / 设计消耗 列（第 4、5 列）
  if ([4, 5].includes(columnIndex)) {
    const customerId = record.customerId;
    const operation = record.operationName;
    const design = record.designName;

    // 占位符处理
    if (!design || design === '' || record.designCost == null) {
      record.designName = record.designName || '/';
      record.designCost = record.designCost ?? '/';
      return { rowspan: 1, colspan: 1 };
    }

    const sameDesignRows = tableData.value.filter(
      (item) =>
        item.customerId === customerId &&
        item.operationName === operation &&
        item.designName === design,
    );
    const firstRowIndex = tableData.value.findIndex(
      (item) =>
        item.customerId === customerId &&
        item.operationName === operation &&
        item.designName === design,
    );
    const rowCount = sameDesignRows.length;

    if (rowIndex === firstRowIndex) {
      return { rowspan: rowCount, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  return { rowspan: 1, colspan: 1 };
};
const rowHeight = 40;
const formData = ref(props.formData ?? {});


// 工具函数
function normalizeVal(v: any) {
  if (v === null || v === undefined) return '';
  return String(v).trim();
}

function sortForGrouping(data: any[]) {
  return data.slice().sort((a, b) => {
    // 先按 customerId 排
    const ca = Number(a.customerId || 0);
    const cb = Number(b.customerId || 0);
    if (ca !== cb) return ca - cb;

    // 再按 operationName 排
    const oa = normalizeVal(a.operationName);
    const ob = normalizeVal(b.operationName);
    const opCmp = oa.localeCompare(ob, 'zh-CN');
    if (opCmp !== 0) return opCmp;

    // 最后按 designName 排
    const da = normalizeVal(a.designName);
    const db = normalizeVal(b.designName);
    return da.localeCompare(db, 'zh-CN');
  });
}




watch(
  () => props.formData,
  (newVal) => {
    formData.value = newVal ?? {};
  },
);

// Add watcher for specific keys in formData
watch(
  () => [formData.value.area, formData.value.startDate, formData.value.endDate],
  () => {
    getGatherDataList();
  },
);

async function getGatherDataList() {
  loading.value = true;
  try {
    try {
      const res = await getGatherDataListApi(formData.value);
      tableData.value = sortForGrouping(res.data || []);
      console.log(res, 'res');
    } catch (error) {
      console.error('Error fetching gather data list:', error);
      tableData.value = [];
    }
  } finally {
    loading.value = false;
  }
}
onMounted(() => {
  getGatherDataList();
});

function handleScroll(e: Event) {
  const target = e.target as HTMLElement;
  const { scrollTop, clientHeight, scrollHeight } = target;
  if (scrollTop + clientHeight >= scrollHeight - 1) {
    console.log('触发触底事件');
    emit('reachBottom');
  }
}

onMounted(() => {
  const body = tableRef.value?.$el.querySelector('.arco-table-body');
  if (body) {
    body.addEventListener('scroll', handleScroll, { passive: true });
  }
});

onBeforeUnmount(() => {
  const body = tableRef.value?.$el.querySelector('.arco-table-body');
  if (body) {
    body.removeEventListener('scroll', handleScroll);
  }
});
defineExpose({
  refresh: getGatherDataList,
});
</script>

<style scoped>
:deep(.highlight-row td) {
  background-color: #f5f5f5 !important;
  transition: 0.5s all ease;
}
.table-scroll-container {
  /* overflow-y: auto; */
  position: relative;
  /* border: 1px solid #e5e7eb; */
  border-radius: 4px;
}

.merge-cell {
  display: inline-block;
  width: 100%;
  height: 100%;
  line-height: v-bind(rowHeight + 'px');
  text-align: center;
  background-color: #f0f9ff;
  border-radius: 2px;
}
.loading-tip,
.no-more-tip {
  text-align: center;
  padding: 15px 0;
  color: #606266;
  background-color: #fafafa;
}
:deep(.el-table__row) {
  height: v-bind(rowHeight + 'px') !important;
}
</style>