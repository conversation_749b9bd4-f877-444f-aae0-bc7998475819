<template>
  <div
    class="table-scroll-container"
    ref="scrollContainer"
    @scroll="handleScroll"
    :style="{ height: tableHeight }"
  >
    <GiTable
      :data="tableData"
      :span-method="handleSpanMerge"
      border
      row-key="uniqueId"
      style="width: 100%;"
    >
    
    </GiTable>

    <!-- 加载提示 -->
    <div v-if="loading" class="loading-tip">加载中...</div>
    <div v-if="!hasMore && tableData.length > 0" class="no-more-tip">没有更多数据了</div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineExpose } from "vue";
import type { PropType } from "vue";

export interface TableRow {
  uniqueId: string;
  customerId: string;
  customerName: string;
  totalConsumption: number;
  operatorName: string;
  operatorConsumption: number;
  designerName: string;
  designerConsumption: number;
  isFirstDesign?: boolean;
}

const props = defineProps({
  initialData: {
    type: Array as PropType<TableRow[]>,
    default: () => [],
  },
  fetchApi: {
    type: Function as PropType<
      (page: number) => Promise<{ data: TableRow[]; hasMore: boolean }>
    >,
    required: true,
  },
  tableHeight: { type: String, default: "600px" },
  rowHeight: { type: Number, default: 48 },
});

const emit = defineEmits(["data-loaded", "error"]);

const tableData = ref<TableRow[]>([]);
const scrollContainer = ref<HTMLElement | null>(null);
const loading = ref(false);
const hasMore = ref(true);
const mergeConfig = ref<Record<number, any>>({});
const currentPage = ref(1);

// 监听初始数据
watch(
  () => props.initialData,
  (newVal) => {
    if (Array.isArray(newVal)) {
      tableData.value = [...newVal];
      calcMergeConfig(tableData.value);
      emit("data-loaded", tableData.value);
    }
  },
  { immediate: true }
);

onMounted(() => {
  if (!props.initialData || props.initialData.length === 0) {
    fetchMoreData();
  }
});

// 计算合并配置
const calcMergeConfig = (fullData: TableRow[]) => {
  const config: Record<number, any> = {};
  const customerRowMap: Record<string, { startRowIndex: number; rowCount: number }> = {};

  fullData.forEach((row, rowIndex) => {
    if (!row.customerId) return;
    if (!customerRowMap[row.customerId]) {
      customerRowMap[row.customerId] = { startRowIndex: rowIndex, rowCount: 0 };
    }
    customerRowMap[row.customerId].rowCount++;
  });

  fullData.forEach((row, rowIndex) => {
    if (!row.customerId) return;
    const { startRowIndex, rowCount } = customerRowMap[row.customerId] || {};
    config[rowIndex] = {};

    // 合并列
    [1, 2, 3].forEach((colIndex) => {
      if (rowIndex === startRowIndex) {
        config[rowIndex][colIndex] = { rowspan: rowCount, colspan: 1 };
      } else {
        config[rowIndex][colIndex] = { rowspan: 0, colspan: 0 };
      }
    });

    // 其他列不合并
    [0, 4, 5].forEach((colIndex) => {
      config[rowIndex][colIndex] = { rowspan: 1, colspan: 1 };
    });
  });

  mergeConfig.value = config;
};

// 合并方法
const handleSpanMerge = ({ rowIndex, columnIndex }: any) => {
  return mergeConfig.value[rowIndex]?.[columnIndex] || { rowspan: 1, colspan: 1 };
};

// 触底加载更多
const handleScroll = () => {
  const container = scrollContainer.value;
  if (!container || loading.value || !hasMore.value) return;

  const isReachBottom =
    container.scrollTop + container.clientHeight >= container.scrollHeight - 10;

  if (isReachBottom) {
    fetchMoreData();
  }
};

// 获取更多数据
const fetchMoreData = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    const result = await props.fetchApi(currentPage.value);
    if (result && result.data && result.data.length > 0) {
      tableData.value = [...tableData.value, ...result.data];
      calcMergeConfig(tableData.value);
      currentPage.value++;
      hasMore.value = result.hasMore !== undefined ? result.hasMore : true;
      emit("data-loaded", tableData.value);
    } else {
      hasMore.value = false;
    }
  } catch (err) {
    console.error("加载数据失败:", err);
    emit("error", err);
  } finally {
    loading.value = false;
  }
};

// 提供重置
const resetTable = () => {
  tableData.value = [];
  currentPage.value = 1;
  hasMore.value = true;
  mergeConfig.value = {};
  fetchMoreData();
};

defineExpose({ resetTable });
</script>

<style scoped>
.table-scroll-container {
  overflow-y: auto;
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

.merge-cell {
  display: inline-block;
  width: 100%;
  height: 100%;
  line-height: v-bind(rowHeight + "px");
  text-align: center;
  background-color: #f0f9ff;
  border-radius: 2px;
}
.loading-tip,
.no-more-tip {
  text-align: center;
  padding: 15px 0;
  color: #606266;
  background-color: #fafafa;
}
:deep(.el-table__row) {
  height: v-bind(rowHeight + "px") !important;
}
</style>