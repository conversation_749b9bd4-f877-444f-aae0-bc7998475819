<template>
  <div>
    <div class="summary-filter-box">
      <div>
        <h4>查看维度</h4>
        <div
          class="summary-dimension"
          :class="theme"
          style="display: flex;margin-top: 5px;"
        >
          <div class="summary-dimension-group">
            <!-- @click="handleDimensionChange(item.key)" -->
            <div
              v-for="item in dimensionList"
              :key="item.key"
              class="summary-dimension-item"
              :class="{ active: item.key === activeDimension }"

            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <div>
        <h4>时间</h4>
        <!-- <a-range-picker
          v-model="formData.timeRange"
          style="width: 300px;margin-top: 5px;"
          shortcuts-position="right"
          :shortcuts="rangeShortcuts"
          @change="handleTimeChange"
          :allow-clear="false"
        /> -->
      </div>
    </div>

    <a-collapse
      :default-active-key="['1']"
      :expand-icon-position="position"
      :show-expand-icon="!hideIcon"
      :bordered="true"
    >
      <a-collapse-item key="1">
        <template #header>
          <h4><icon-filter /> 高级筛选</h4>
        </template>

        <div class="summary-filter-item">
          <div>
            <p>客户</p>
            <!-- @clear="getSummaryDataList" -->
            <a-select
              placeholder="请选择客户"
              v-model="formData.customerId"
              allow-clear
              allow-search

            >
              <a-option
                v-for="item in generalStore.customerList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </div>

          <div>
            <p>媒体</p>
            <!-- @clear="getSummaryDataList" -->
            <a-select
              placeholder="请选择媒体"
              v-model="formData.media"
              allow-clear
              allow-search
              @change="handleMediaChange"

            >
              <a-option
                v-for="item in mediaList"
                :key="item.id"
                :value="item.name"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </div>

          <div style="display: flex;flex-direction: row;align-items: flex-end;justify-content: center;">
            <!-- @click="getSummaryDataList" -->
            <a-button

              type="primary"
              style="margin-right: 20px;"
            >查询</a-button>
            <a-button @click="reset">重置</a-button>
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
    <div>
      <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 30px;">
        <div >
          消耗汇总：<span style="font-weight: bold;">{{formatNumber(totalCost)}}</span>
        </div>

        <a-button type="primary"  :loading="btnLoading">
             <!-- @click="exportData" -->
          导出
          <template #icon>
            <icon-download />
          </template>
          </a-button>
      </div>
      <!-- 初始表格 -->
      <!-- <GiTable
        max-height="500"
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >
        <template #cell-cost="{ record }">
          {{ formatNumber(record.cost) }}
        </template>
        <template #cell-yesterdayCost="{ record }">
          {{ formatNumber(record.yesterdayCost) }}
        </template>
        <template #cell-lastSevenDaysCost="{ record }">
          {{ formatNumber(record.lastSevenDaysCost) }}
        </template>
      </GiTable> -->
        <MergedInfiniteTable
            :initialData="initialData"
            :fetch-api="fetchTableData"
            table-height="500px"
        />
    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
defineOptions({ name: 'SummaryData' });
import { ref, computed, defineExpose, reactive, onMounted } from 'vue';
import { useGeneralDataStore } from '@/stores/generalData';
// import { exportSummaryDataListApi, getSummaryDataListApi } from '@/apis/dataSummary';
import { Message } from '@arco-design/web-vue';
import { formatNumber } from '@/utils/formatNumber';
import { useAppStore } from '@/stores/useAppStore';


import type { TableRow } from "../compUtils/types";
import MergedInfiniteTable from "../compUtils/MergedInfiniteTable.vue";
// 模拟 20 条数据（按客户分组，每个客户 4 个设计）
const initialData = ref<TableRow[]>(Array.from({ length: 20 }, (_, i) => {
  const customerId = `CUS-${Math.floor(i / 4) + 1}`;
  const isFirstDesign = i % 4 === 0;
  return {
    uniqueId: `U-${i}`,
    customerId,
    customerName: `客户${Math.floor(i / 4) + 1}`,
    totalConsumption: 10000 + Math.floor(i / 4) * 1000,
    operatorName: `运营${Math.floor(i / 4) + 1}`,
    operatorConsumption: 10000 + Math.floor(i / 4) * 1000,
    designerName: `设计${i + 1}`,
    designerConsumption: 2000 + (i % 4) * 500,
    isFirstDesign,
  };
}));

// 模拟接口请求
const fetchTableData = async (page: number) => {
  console.log("加载第", page, "页数据");
  // 模拟每页 10 条数据
  const newData: TableRow[] = Array.from({ length: 10 }, (_, i) => {
    const index = (page - 1) * 10 + i;
    const customerId = `CUS-${Math.floor(index / 4) + 1}`;
    const isFirstDesign = index % 4 === 0;
    return {
      uniqueId: `U-${index}`,
      customerId,
      customerName: `客户${Math.floor(index / 4) + 1}`,
      totalConsumption: 10000 + Math.floor(index / 4) * 1000,
      operatorName: `运营${Math.floor(index / 4) + 1}`,
      operatorConsumption: 10000 + Math.floor(index / 4) * 1000,
      designerName: `设计${index + 1}`,
      designerConsumption: 2000 + (index % 4) * 500,
      isFirstDesign,
    };
  });

  return {
    data: newData,
    hasMore: page < 3, // 模拟最多 3 页
  };
};



const appStore = useAppStore();
const theme = computed(() => appStore.theme);
const formData = ref({
  viewDimension: 1,
  dataDimension: 1,
  timeRange: [
    dayjs().subtract(1, 'day').startOf('day'),
    dayjs().subtract(1, 'day').endOf('day'),
  ],
  media: '',
  customerId: '',
  ownerUserId: '',
});
interface SummaryDataItem {
  customerName: string;
  cost: number | string;
  yesterdayCost: number | string;
  lastSevenDaysCost: number | string;
  yearOnYearCost: number | string;
  monthOnMonthCost: number | string;
}
const dimensionList = [
  { key: 1, label: '汇总数据' },
  { key: 2, label: '分日数据' },
];
const activeDimension = ref(1);
const rangeShortcuts = [
  {
    label: '今天',
    value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '昨天',
    value: () => [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '七天',
    value: () => [
      dayjs().subtract(7, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];
// function handleDimensionChange(key: number) {
//   activeDimension.value = key;
//   formData.value.dataDimension = key;
//   getSummaryDataList();
// }
// function handleTimeChange() {
//   getSummaryDataList();
// }
const position = ref('right');
const hideIcon = ref(false);

const generalStore = useGeneralDataStore();
const mediaList = computed(() =>
  generalStore.mediaList.filter((m) => m.visible),
);

const dataList = ref<SummaryDataItem[]>([]);

const columns = computed(() => {
  const allCols = [
    {
      dataIndex: 'date',
      title: '日期',
      align: 'center',
      width: 170,
      showWhen: [2],
    },
    {
      dataIndex: 'customerName',
      title: '客户',
      align: 'center',
      width: 170,
      showWhen: [1, 2],
    },
    {
      dataIndex: 'media',
      title: '媒体',
      align: 'center',
      width: 130,
      showWhen: [1, 2],
    },
    {
      dataIndex: 'cost',
      title: '消耗',
      align: 'center',
      width: 130,
      sortable: { sortDirections: ['ascend', 'descend'] },
      showWhen: [1, 2],
      slotName: 'cell-cost',
    },
    {
      dataIndex: 'yesterdayCost',
      title: '昨日消耗',
      align: 'center',
      width: 130,
      sortable: { sortDirections: ['ascend', 'descend'] },
      showWhen: [2],
      slotName: 'cell-yesterdayCost',
    },
    {
      dataIndex: 'lastSevenDaysCost',
      title: '近七日消耗',
      align: 'center',
      width: 130,
      sortable: { sortDirections: ['ascend', 'descend'] },
      showWhen: [1, 2],
      slotName: 'cell-lastSevenDaysCost',
    },
    {
      dataIndex: 'ownerUserName',
      title: '运营',
      align: 'center',
      width: 130,
      showWhen: [1, 2],
      slotName: 'cell-owner',
    },
  ];
  return allCols.filter((col) => col.showWhen.includes(activeDimension.value));
});

const btnLoading = ref(false);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const selectedKeys = ref([]); // 选中行的key

function handlePageChange(page) {
  console.log('当前页码:', page);
  pagination.currentPage = page;
  //   getSummaryDataList();
}

function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  //   getSummaryDataList();
}

function handleMediaChange(val: number) {
  const selected = generalStore.mediaList.find((m) => m.id === val);
  console.log('选择的媒体:', selected ? selected.name : '');
}

if (!generalStore.customerList.length) {
  generalStore.fetchCustomerList();
}

// 导出
// async function exportData() {
//   // 校验是否有数据
//   if (!dataList.value || dataList.value.length === 0) {
//     Message.warning('暂无数据可导出');
//     return;
//   }

//   btnLoading.value = true; // 开始 loading
//   try {
//     const res = await exportSummaryDataListApi(formData.value);

//     const blob = new Blob([res.data], {
//       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     });
//     const url = window.URL.createObjectURL(blob);

//     // 创建 a 标签下载
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = `汇总数据报表_${Date.now()}.xlsx`; // 自定义文件名
//     a.click();

//     // 释放 URL 对象
//     window.URL.revokeObjectURL(url);

//     Message.success('导出成功');
//   } catch (e) {
//     console.error('导出失败', e);
//     Message.error('导出失败，请稍后重试');
//   } finally {
//     btnLoading.value = false; // 关闭 loading
//   }
// }

const totalCost = ref(0);

// async function getSummaryDataList() {
//   loading.value = true;
//   const { timeRange, ...rest } = formData.value;
//   const params = {
//     ...rest,
//     timeRange: timeRange.map(t => dayjs(t).format('YYYY-MM-DD')),
//     page: pagination.currentPage,
//     size: pagination.pageSize,
//   };
//   const res = await getSummaryDataListApi(params);
//   if (res.code === '0') {
//     dataList.value = res.data.page.list;
//     pagination.total = res.data.page.total;
//     totalCost.value = res.data.totalCost;
//     console.log(res, 'res');
//   } else {
//     Message.error(res.msg || '获取汇总数据失败');
//   }
//   loading.value = false;
// }
// onMounted(() => {
//   getSummaryDataList();
// });

function reset() {
//   formData.value = {
//     viewDimension: 2,
//     dataDimension: 1,
//     timeRange: [
//       dayjs().subtract(1, 'day').startOf('day'),
//       dayjs().subtract(1, 'day').endOf('day'),
//     ],
//     media: '',
//     customerId: '',
//     ownerUserId: '',
//   };
//   activeDimension.value = 1;
//     getSummaryDataList();
}
</script>
<style>
.summary-filter-box {
  display: grid;
  grid-template-columns: max-content max-content;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: start;
  justify-items: start;
  align-items: start;
}

.summary-filter-box > div {
  justify-self: start;
  align-self: start;
  width: max-content;
}
@media screen and (max-width: 678px) {
  .summary-filter-box {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
  .summary-filter-box > div {
    width: 100%;
  }
}

.summary-filter-item {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  justify-content: start;
  margin-bottom: 20px;
}

@media screen and (max-width: 678px) {
  .summary-filter-item {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
}

.summary-dimension-group {
  display: flex;
  border: 1px solid #eee;
  overflow: hidden;
  font-weight: bold;
  justify-content: flex-start;
}

.summary-dimension-item {
  cursor: pointer;
  user-select: none;
  padding: 7px 15px;
  flex: 1;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #666;
  background: #fff;
  position: relative;
}

.summary-dimension-item:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}

.summary-dimension-item.active {
  background: #2d80ff;
  color: #fff;
  z-index: 1;
}

.summary-dimension-item:active {
  transform: scale(0.98);
}
/* --- 维度选择 chips 的主题态 --- */
.summary-dimension.light .summary-dimension-group {
  border-color: #eee;
}
.summary-dimension.dark .summary-dimension-group {
  border-color: #2a2a2a;
}

.summary-dimension.light .summary-dimension-item {
  color: #666;
  background: #fff;
}
.summary-dimension.dark .summary-dimension-item {
  color: #cfcfcf;
  background: #1f1f1f;
}

.summary-dimension.light .summary-dimension-item:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}
.summary-dimension.dark .summary-dimension-item:hover:not(.active) {
  background: #2a3344;
  color: #9ec2ff;
}

/* active 选中态两边主题一致，用主色 */
.summary-dimension.light .summary-dimension-item.active,
.summary-dimension.dark .summary-dimension-item.active {
  background: #2d80ff;
  color: #fff;
}
</style>
