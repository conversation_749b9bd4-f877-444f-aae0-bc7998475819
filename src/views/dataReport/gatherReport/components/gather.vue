<template>
  <div>
    <div class="summary-filter-box">
      <div>
        <h4>查看维度</h4>
        <div
          class="summary-dimension"
          :class="theme"
          style="display: flex;margin-top: 5px;"
        >
          <div class="summary-dimension-group">
            <!--  -->
            <div
              @click="handleDimensionChange(item.key)"
              v-for="item in dimensionList"
              :key="item.key"
              class="summary-dimension-item"
              :class="{ active: item.key === activeDimension }"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <div style="height: 53px;display: flex;flex-direction: column;align-items: flex-start;justify-content: space-between;">
        <h4 >区域</h4>
        <a-select
          placeholder="请选择区域"
          v-model="formData.area"
          allow-clear
          allow-search
          style="width: 223px;"
        >
          <a-option
            v-for="item in source"
            :key="item.value"
            :value="item.value"
          >
            {{ item.area }}
          </a-option>
        </a-select>
      </div>
      <div>
        <h4>时间</h4>
        <a-range-picker
          style="width: 300px;margin-top: 5px;"
          shortcuts-position="right"
          :shortcuts="rangeShortcuts"
          @change="handleTimeChange"
          :value="[formData.startDate,formData.endDate]"
          :allow-clear="false"
          :default-value="[dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')]"
        />
      </div>

    </div>
    <a-collapse
      :default-active-key="['1']"
      :expand-icon-position="position"
      :show-expand-icon="!hideIcon"
      :bordered="true"
    >
      <a-collapse-item key="1">
        <template #header>
          <h4><icon-filter /> 高级筛选</h4>
        </template>

        <div class="summary-filters">
          <div class="summary-filter-item">
            <div>
              <p>客户</p>
              <!--  -->
              <a-select
                placeholder="请选择客户"
                v-model="formData.customerId"
                allow-clear
                allow-search
                @clear="handleSearch"

              >
                <a-option
                  v-for="item in generalStore.customerList"
                  :key="item.id"
                  :value="item.id"
                >
                  {{ item.name }}
                </a-option>
              </a-select>
            </div>
            <div>
              <p>媒体</p>
              <!-- @clear="getSummaryDataList" -->
              <a-select
                placeholder="请选择媒体"
                v-model="formData.media"
                allow-clear
                allow-search
                @change="handleMediaChange"
                @clear="handleSearch"
              >
                <a-option
                  v-for="item in mediaList"
                  :key="item.id"
                  :value="item.name"
                >
                  {{ item.name }}
                </a-option>
              </a-select>
            </div>
            <div>
              <p>优化</p>
              <a-input
                v-model="formData.optimize"
                placeholder="请输入优化姓名"
                allow-clear
                @clear="handleSearch"
                @input="handleInput('optimize')"
              ></a-input>
            </div>
            <div>
              <p>设计</p>
              <a-input
                v-model="formData.design"
                placeholder="请输入设计姓名"
                allow-clear
                @clear="handleSearch"
                @input="handleInput('design')"
              ></a-input>
            </div>
          </div>
          <div style="display: flex;flex-direction: row;align-items: flex-end;justify-content: center;">
            <!-- @click="getSummaryDataList" -->
            <a-button
              type="primary"
              style="margin-right: 20px;"
              @click="handleSearch"
              :disabled="!formData.customerId && !formData.media && !formData.optimize && !formData.design"
            >查询</a-button>
            <a-button
              @click="reset"
              :disabled="!formData.customerId && !formData.media && !formData.optimize && !formData.design"
            >重置</a-button>
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
    <!-- <div>
      <div style="display: flex;flex-direction: row;justify-content: flex-end;align-items: center;margin: 20px 0;">
        <a-button type="primary" @click="exportData"  :loading="btnLoading">
          导出
          <template #icon>
            <icon-download />
          </template>
        </a-button>
      </div>
    </div> -->
    <MergedInfiniteTable
      ref="mergedTable"
      :tableHeight="getViewportHeight()-500"
      @reachBottom="loadMoreData"
      :formData="formData"
      style="margin-top: 30px;"
    />
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
defineOptions({ name: 'SummaryData' });
import { ref, computed, defineExpose, reactive, onMounted } from 'vue';
import { useGeneralDataStore } from '@/stores/generalData';
// import { exportSummaryDataListApi, getSummaryDataListApi } from '@/apis/dataSummary';
import { Message } from '@arco-design/web-vue';
import { useAppStore } from '@/stores/useAppStore';
import MergedInfiniteTable from '../compUtils/MergedInfiniteTable.vue';
import { useResponsive } from '@/mixins/responsive';
const mergedTable = ref();
const source = ref([
  { area: '华东区域', value: '华东' },
  { area: '华北区域', value: '华北' },
]);
const { getViewportHeight } = useResponsive();
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
const formData = ref({
  page:1,
  pageSize:10,
  area: '',
  startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  customerId: '', //客户id
  media: '', //媒体id
  optimize: '', //优化
  design: '', //设计
});

function loadMoreData() {
  // console.log('加载更多数据',getViewportHeight());
}

const dimensionList = [
  { key: 1, label: '汇总数据' },
  { key: 2, label: '分日数据' },
];
const activeDimension = ref(1);
const rangeShortcuts = [
  {
    label: '今天',
    value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '昨天',
    value: () => [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '七天',
    value: () => [
      dayjs().subtract(7, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];
function handleDimensionChange(key: number) {
  activeDimension.value = key;
}
function handleTimeChange(value) {
  formData.value.startDate = value[0];
  formData.value.endDate = value[1];
  console.log(formData.value);
}
const position = ref('right');
const hideIcon = ref(false);

const generalStore = useGeneralDataStore();
const mediaList = computed(() =>
  generalStore.mediaList.filter((m) => m.visible),
);
const btnLoading = ref(false);

function handleMediaChange(val: number) {
  const selected = generalStore.mediaList.find((m) => m.id === val);
  console.log('选择的媒体:', selected ? selected.name : '');
}

if (!generalStore.customerList.length) {
  generalStore.fetchCustomerList();
}
function exportData() {
  btnLoading.value = true;
  setTimeout(() => {
    btnLoading.value = false;
  }, 2000);
}
// 导出
// async function exportData() {
//   // 校验是否有数据
//   if (!dataList.value || dataList.value.length === 0) {
//     Message.warning('暂无数据可导出');
//     return;
//   }

//   btnLoading.value = true; // 开始 loading
//   try {
//     const res = await exportSummaryDataListApi(formData.value);

//     const blob = new Blob([res.data], {
//       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     });
//     const url = window.URL.createObjectURL(blob);

//     // 创建 a 标签下载
//     const a = document.createElement('a');
//     a.href = url;
//     a.download = `汇总数据报表_${Date.now()}.xlsx`; // 自定义文件名
//     a.click();

//     // 释放 URL 对象
//     window.URL.revokeObjectURL(url);

//     Message.success('导出成功');
//   } catch (e) {
//     console.error('导出失败', e);
//     Message.error('导出失败，请稍后重试');
//   } finally {
//     btnLoading.value = false; // 关闭 loading
//   }
// }

function reset() {
  formData.value = {
    area: '',
    startDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    customerId: '', //客户id
    media: '', //媒体id
    optimize: '', //优化
    design: '', //设计
  };
  handleSearch();
}
function handleInput(type: 'optimize' | 'design') {
  const value = formData.value[type];
  if (value === '') {
    console.log(`${type}为空`);
    handleSearch();
  }
}
function handleSearch() {
  mergedTable.value.refresh();
}
</script>
<style>
.summary-filter-box {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.summary-filter-box > div {
  justify-self: start;
  align-self: start;
  width: max-content;
}
@media screen and (max-width: 678px) {
  .summary-filter-box {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
  .summary-filter-box > div {
    width: 100%;
  }
}

.summary-filters {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  gap: 20px;
  flex-wrap: wrap;
}

.summary-filter-item {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  justify-content: start;
}

@media screen and (max-width: 678px) {
  .summary-filter-item {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
}

.summary-dimension-group {
  display: flex;
  border: 1px solid #eee;
  overflow: hidden;
  font-weight: bold;
  justify-content: flex-start;
}

.summary-dimension-item {
  cursor: pointer;
  user-select: none;
  padding: 7px 15px;
  flex: 1;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #666;
  background: #fff;
  position: relative;
}

.summary-dimension-item:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}

.summary-dimension-item.active {
  background: #2d80ff;
  color: #fff;
  z-index: 1;
}

.summary-dimension-item:active {
  transform: scale(0.98);
}
/* --- 维度选择 chips 的主题态 --- */
.summary-dimension.light .summary-dimension-group {
  border-color: #eee;
}
.summary-dimension.dark .summary-dimension-group {
  border-color: #2a2a2a;
}

.summary-dimension.light .summary-dimension-item {
  color: #666;
  background: #fff;
}
.summary-dimension.dark .summary-dimension-item {
  color: #cfcfcf;
  background: #1f1f1f;
}

.summary-dimension.light .summary-dimension-item:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}
.summary-dimension.dark .summary-dimension-item:hover:not(.active) {
  background: #2a3344;
  color: #9ec2ff;
}

/* active 选中态两边主题一致，用主色 */
.summary-dimension.light .summary-dimension-item.active,
.summary-dimension.dark .summary-dimension-item.active {
  background: #2d80ff;
  color: #fff;
}
</style>
