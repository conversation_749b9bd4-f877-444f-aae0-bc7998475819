<template>
  <div class="gi_page">
    <a-card >
      <a-tabs default-active-key="1" >
        <a-tab-pane key="1">
          <template #title>
            <icon-user /> 客户
          </template>
          <Customer   />
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #title>
            <icon-common /> 产品
          </template>
          <Product    />
        </a-tab-pane>
        <a-tab-pane key="3">
          <template #title>
            <icon-command /> 账户
          </template>
          <Account    />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>
<script lang="ts" setup>
import Customer from './components/customer.vue';
import Account from './components/account.vue';
import Product from './components/product.vue';
defineOptions({ name: 'ACReport' });
</script>
<style scoped>

</style>