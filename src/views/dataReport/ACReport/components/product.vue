<template>
  <div>
    <div class="filter-box">
      <div>
        <h4>查看纬度</h4>
        <div class="active-dimension" :class="theme" style="display: flex;margin-top: 5px;">
          <div class="dim-chip-group">
            <div
              v-for="item in dimensionList"
              :key="item.key"
              class="dim-chip"
              :class="{ active: item.key === activeDimension }"
              @click="handleDimensionChange(item.key)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>
      <div>
        <h4>时间</h4>
        <a-range-picker
          v-model="formData.timeRange"
          style="width: 300px;margin-top: 5px;"
          shortcuts-position="right"
          :shortcuts="rangeShortcuts"
          @change="handleTimeChange"
          :allow-clear="false"
        />
      </div>
    </div>

    <a-collapse
      :default-active-key="['1']"
      :expand-icon-position="position"
      :show-expand-icon="!hideIcon"
      :bordered="true"
    >
      <a-collapse-item key="1" >
        <template #header>
          <h4><icon-filter /> 高级筛选</h4>
        </template>
        <div class="filter-box-item">
          <!-- 客户 -->
          <div>
            <p>客户</p>
            <a-select
              placeholder="请选择客户"
              v-model="formData.customerId"
              allow-clear
              allow-search
              @clear="getACReportList"
            >
              <a-option
                v-for="item in generalStore.customerList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </div>
          <!-- 产品 -->
          <div>
            <p>产品</p>
            <a-select
              placeholder="请选择产品"
              v-model="formData.productId"
              allow-clear
              allow-search
              :disabled="!formData.customerId"
               @clear="getACReportList"
            >
              <a-option
                v-for="item in industryList"
                :key="item.id"
                :value="item.id"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </div>
          <!-- 媒体 -->
          <div>
            <p>媒体</p>
            <a-select
              placeholder="请选择媒体"
              v-model="formData.media"
              allow-clear
              allow-search
              @change="handleContractChange"
              @clear="getACReportList"
            >
              <a-option
                v-for="item in mediaList"
                :key="item.id"
                :value="item.name"
              >
                {{ item.name }}
              </a-option>
            </a-select>
          </div>

          <div style="display: flex;flex-direction: row;align-items: flex-end;justify-content: center;">
            <a-button
              @click="getACReportList"
              type="primary"
              style="margin-right: 20px;"
            >查询</a-button>
            <a-button @click="reset">重置</a-button>
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
    <div>
      <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 30px;">
        <div >
          消耗汇总：<span style="font-weight: bold;">{{formatNumber(totalCost)}}</span>
        </div>
        <a-button type="primary"  @click="exportData" :loading="btnLoading">
          导出
          <template #icon>
            <icon-download />
          </template>
          </a-button>
      </div>
      <GiTable
        :data="dataList"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
        row-key="key"
        v-model:selectedKeys="selectedKeys"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      >


      <template #cell-cost="{ record }">
        {{ formatNumber(record.cost) }}
      </template>
      <template #cell-yesterdayCost="{ record }">
        {{ formatNumber(record.yesterdayCost) }}
      </template>
      <template #cell-lastSevenDaysCost="{ record }">
        {{ formatNumber(record.lastSevenDaysCost) }}
      </template>
      <template #cell-owner="{record}">
        <span v-for="(item, index) in record.operators" :key="item.id">
          {{ item.name }}<span v-if="index < record.operators.length - 1">,</span>
        </span>
      </template>
      </GiTable>

    </div>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs';
defineOptions({ name: 'Product' });
import { ref, computed, defineExpose, reactive, onMounted } from 'vue';
import { useGeneralDataStore } from '@/stores/generalData';
import { exportACReportListApi, getACReportListApi, getProductListApi } from '@/apis/dataReport';
import { Message } from '@arco-design/web-vue';
import { useAppStore } from '@/stores/useAppStore';
import { formatNumber } from '@/utils/formatNumber';
const appStore = useAppStore();
const theme = computed(() => appStore.theme);

const formData = ref({
  viewDimension: 2,
  dataDimension: 1,
  timeRange: [
    dayjs().subtract(1, 'day').startOf('day'),
    dayjs().subtract(1, 'day').endOf('day'),
  ],
  media: '',
  productId: '',
  customerId: '',
});
const dimensionList = [
  { key: 1, label: '汇总数据' },
  { key: 2, label: '分日数据' },
];
const activeDimension = ref(1);
const rangeShortcuts = [
  {
    label: '今天',
    value: () => [dayjs().startOf('day'), dayjs().endOf('day')],
  },
  {
    label: '昨天',
    value: () => [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '七天',
    value: () => [
      dayjs().subtract(7, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
  },
  {
    label: '本月',
    value: () => [dayjs().startOf('month'), dayjs().endOf('month')],
  },
  {
    label: '上月',
    value: () => [
      dayjs().subtract(1, 'month').startOf('month'),
      dayjs().subtract(1, 'month').endOf('month'),
    ],
  },
];
function handleDimensionChange(key: number) {
  activeDimension.value = key;
  formData.value.dataDimension = key;
  getACReportList();
}
function handleTimeChange() {
  getACReportList();
}
const position = ref('right');
const hideIcon = ref(false);

const generalStore = useGeneralDataStore();
const mediaList = computed(() =>
  generalStore.mediaList.filter((m) => m.visible),
);
interface ACReportItem {
  adAccountId: string | number;
  adAccountName: string;
  customerName: string;
  productName: string;
  media: string;
  cost: string | number;
  todayCost: string | number;
  yesterdayCost: string | number;
  lastThreeDaysCost: string | number;
  lastSevenDaysCost: string | number;
  yearOnYearCost: string | number;
  monthOnMonthCost: string | number;
  ownerUserName?: string;
}

const dataList = ref<ACReportItem[]>([]);
const columns = computed(() => {
  const allCols = [
    { dataIndex: 'date', title: '日期', align: 'center', width: 170, showWhen: [ 2] },
    { dataIndex: 'customerName', title: '客户', align: 'center', width: 170, showWhen: [1, 2] },
    { dataIndex: 'productName', title: '产品', align: 'center', width: 130, showWhen: [1, 2] },
    { dataIndex: 'media', title: '媒体', align: 'center', width: 130, showWhen: [1, 2] },
    { dataIndex: 'cost', title: '消耗', align: 'center', width: 130, showWhen: [1, 2], slotName: 'cell-cost' },
    { dataIndex: 'yesterdayCost', title: '昨日消耗', align: 'center', width: 130, sortable: { sortDirections: ['ascend', 'descend'] }, showWhen: [2], slotName: 'cell-yesterdayCost' },
    { dataIndex: 'lastSevenDaysCost', title: '近七日消耗', align: 'center', width: 130, sortable: { sortDirections: ['ascend', 'descend'] }, showWhen: [1, 2] ,slotName: 'cell-lastSevenDaysCost' },
    { dataIndex: 'ownerUserName', title: '运营', align: 'center', width: 130, showWhen: [1, 2], slotName: 'cell-owner' },
  ];
  return allCols.filter(col => col.showWhen.includes(activeDimension.value));
});

const btnLoading = ref(false);
const loading = ref(false);
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize: 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: true, // 显示每页条数选择器
  showJumper: true, // 显示跳转页码输入框
});
const selectedKeys = ref([]); // 选中行的key

const industryList = ref([]);
// async function handleSelect(value) {
//   const res = await getProductListApi({
//     customerId: value,
//   });
//   // 映射接口数据到 industryList
//   industryList.value = (res.data|| []).map((item) => ({
//     id: item.id,
//     name: item.productName,
//   }));
// }

function handleCustomerClear () {
  industryList.value = [];
  formData.value.productId = ''
}


function handlePageChange(page) {
  console.log('当前页码:', page); // 确认是否打印正确页码（如点击第2页应打印 2）
  pagination.currentPage = page;
  getACReportList();
}

function handlePageSizeChange(pageSize) {
  pagination.pageSize = pageSize;
  getACReportList();
}

function handleContractChange(val: number) {
  const selected = generalStore.mediaList.find((m) => m.id === val);
  console.log('选择的媒体:', selected ? selected.name : '');
}

if (!generalStore.customerList.length) {
  generalStore.fetchCustomerList();
}
const totalCost = ref(0)
async function getACReportList() {
  loading.value = true;
  loading.value = true;
  const { timeRange, ...rest } = formData.value;
  const params = {
    ...rest,
    timeRange: timeRange.map(t => dayjs(t).format('YYYY-MM-DD')),
    page: pagination.currentPage, // 后端通常用 pageNum 接收页码
    size: pagination.pageSize,   // 传递每页条数

  };
  const res = await getACReportListApi(params);
  if (res.code === '0') {
    dataList.value = res.data.page.list;
    pagination.total = res.data.page.total;
    totalCost.value = res.data.totalCost;
    console.log(res, 'res');
  } else {
    Message.error(res.msg || '获取数据失败');
  }
  loading.value = false;
}

async function exportData() {
  // 校验是否有数据
  if (!dataList.value || dataList.value.length === 0) {
    Message.warning('暂无数据可导出');
    return;
  }

  btnLoading.value = true; // 开始 loading
  try {
    const res = await exportACReportListApi(formData.value);

    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);

    // 创建 a 标签下载
    const a = document.createElement('a');
    a.href = url;
    a.download = `报表_${Date.now()}.xlsx`; // 自定义文件名
    a.click();

    // 释放 URL 对象
    window.URL.revokeObjectURL(url);

    Message.success('导出成功');
  } catch (e) {
    console.error('导出失败', e);
    Message.error('导出失败，请稍后重试');
  } finally {
    btnLoading.value = false; // 关闭 loading
  }
}
onMounted(() => {
  getACReportList();
});
// defineExpose({
//   refresh: getACReportList,
// });
function reset() {
  formData.value = {
    viewDimension: 2,
    dataDimension: 1,
    timeRange: [
      dayjs().subtract(1, 'day').startOf('day'),
      dayjs().subtract(1, 'day').endOf('day'),
    ],
    media: '',
    productId: '',
    customerId: '',
  };
  activeDimension.value = 1;
  getACReportList();
}
// 可隐藏某一条媒体
// generalStore.toggleMediaVisibility(1, false);
</script>
<style>
.filter-box {
  display: grid;
  grid-template-columns: max-content max-content;
  gap: 20px;
  margin-bottom: 20px;
  justify-content: start;
  justify-items: start;
  align-items: start;
}

.filter-box > div {
  justify-self: start;
  align-self: start;
  width: max-content;
}
@media screen and (max-width: 678px) {
  .filter-box {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
  .filter-box > div {
    width: 100%;
  }
}

.filter-box-item {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  justify-content: start;
  margin-bottom: 20px;
}

@media screen and (max-width: 678px) {
  .filter-box-item {
    grid-template-columns: repeat(1, 1fr);
    gap: 10px;
  }
}

.dim-chip-group {
  display: flex;
  border: 1px solid #eee;
  overflow: hidden;
  font-weight: bold;
  justify-content: flex-start;
}

.dim-chip {
  cursor: pointer;
  user-select: none;
  padding: 7px 15px;
  flex: 1;
  min-width: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #666;
  background: #fff;
  position: relative;
}

.dim-chip:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}

.dim-chip.active {
  background: #2d80ff;
  color: #fff;
  z-index: 1;
}

.dim-chip:active {
  transform: scale(0.98);
}

/* --- 维度选择 chips 的主题态 --- */
.active-dimension.light .dim-chip-group {
  border-color: #eee;
}
.active-dimension.dark .dim-chip-group {
  border-color: #2a2a2a;
}

.active-dimension.light .dim-chip {
  color: #666;
  background: #fff;
}
.active-dimension.dark .dim-chip {
  color: #cfcfcf;
  background: #1f1f1f;
}

.active-dimension.light .dim-chip:hover:not(.active) {
  background: #f8faff;
  color: #2d80ff;
}
.active-dimension.dark .dim-chip:hover:not(.active) {
  background: #2a3344;
  color: #9ec2ff;
}

/* active 选中态两边主题一致，用主色 */
.active-dimension.light .dim-chip.active,
.active-dimension.dark .dim-chip.active {
  background: #2d80ff;
  color: #fff;
}
</style>