<template>
    <a-card>
        <a-tabs
            size="large"
            default-active-key="1"
            @change="change"
        >
            <a-tab-pane
                key="1"
                title="新客开发"
            >
                <Develop/>
            </a-tab-pane>
             <a-tab-pane
                key="2"
                title="新客增速"
            >
                <GrowthRate/>
            </a-tab-pane>
             <a-tab-pane
                key="3"
                title="新客体量"
            >
                <Volume/>
            </a-tab-pane>
        </a-tabs>
    </a-card>
</template>
<script lang="ts" setup>
    defineOptions({ name: 'NewCustmoer' });
    import Develop from './develop.vue'
    import GrowthRate from './growthRate.vue'
    import Volume from './volume.vue'
    function change(key: string) {
        console.log(key)
    }
</script>
<style>

</style>