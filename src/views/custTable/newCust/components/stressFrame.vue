<template>
  <a-card>
    <div>
      <a-button
        type="primary"
        @click="handleAddCust('add','')"
      >新增</a-button>
    </div>
    <GiTable
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
      row-key="key"
      v-model:selectedKeys="selectedKeys"
      :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #cell-theoreticalCost="{ record }">
        {{ formatNumber(record.theoreticalCost) }}
      </template>
      <template #action="{ record }">
        <a-button
          type="text"
          size="mini"
          @click="() => handleAddCust('edit',record)"
        >编辑</a-button>
        <a-button
          type="text"
          status="danger"
          size="mini"
          @click="() => handleDelete(record)"
        >删除</a-button>
      </template>
    </GiTable>
    <drawerAddFrame
      ref="drawerAddFrameRef"
      @refreshList="fetchList"
    />
  </a-card>
</template>
<script lang="ts" setup>
defineOptions({ name: 'StressFrame' });
import { ref } from 'vue';
import drawerAddFrame from './drawerAddFrame.vue';
import { onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { deletetStressFrameApi, getStressFrameApi } from '@/apis/custTable';
import { isMobile } from '@/utils';
const dataList = ref([]);
const columns = [
  { dataIndex: 'id', title: 'ID', width: 130, align: 'center' },
  { dataIndex: 'groupName', title: '集团名称', width: 130, align: 'center' },
  {
    dataIndex: 'promotionEntity',
    title: '推广主体',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'theoreticalCost',
    title: '理论消耗',
    width: 130,
    align: 'center',
    slotName: 'cell-theoreticalCost',
  },
  { dataIndex: 'sales', title: '销售', width: 130, align: 'center' },
  {
    dataIndex: 'action',
    title: '操作',
    slotName: 'action',
    width: 130,
    align: 'center',
  },
];
// 千分符
function formatNumber(value: number | string) {
  if (value == null || value === '') return '';
  const num = Number(value);
  if (isNaN(num)) return value;
  return num.toLocaleString();
}
const loading = ref(false);
const selectedKeys = ref([]); // 选中行的key
const pagination = reactive({
  currentPage: 1, // 当前页
  pageSize:isMobile() ? 15 : 10, // 每页条数
  total: 0, // 总数（在接口返回后更新）
  showTotal: true, // 显示总数
  showPageSize: isMobile() ? false : true, // 显示每页条数选择器
  showJumper:true, // 显示跳转页码输入框
});
async function getCustDevelop() {
  loading.value = true;
  const params = {
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
  };
  const res = await getStressFrameApi(params);
  dataList.value = res.data.records;
  pagination.total = res.data.total;
  loading.value = false;
}
const drawerAddFrameRef = ref();
function handleAddCust(type: string, record: any) {
  drawerAddFrameRef.value?.open(type, record);
}
function handleDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `是否确认删除账户：${record.promotionEntity}`,
    okText: '确认',
    cancelText: '取消',
    titleAlign: 'center',
    bodyStyle: {
      fontSize: '14px',
      lineHeight: '1.6',
      textAlign: 'center',
    },
    onOk: async () => {
      // 删除逻辑
      const res = await deletetStressFrameApi(record.id);
      if (res.code === '0') {
        Message.success('删除成功');
        getCustDevelop();
      } else {
        Message.error(res.msg);
      }
    },
  });
}
// 点击下一页事件
function handlePageChange(page) {
  console.log(page, 'page');
  pagination.currentPage = page;
  getCustDevelop();
}

// 每页条数改变事件
function handlePageSizeChange(pageSize) {
  console.log(pageSize, 'pageSize');
  pagination.pageSize = pageSize;
  getCustDevelop();
}
function fetchList() {
  getCustDevelop();
}
onMounted(() => {
  getCustDevelop();
});
</script>