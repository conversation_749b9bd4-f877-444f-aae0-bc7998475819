<template>
    <a-drawer
    v-model:visible="visible"
    :title="title"
    :width="isMobile() ? '100%' : 600"
    :footer="false"
    @close="closePopup"
    :unmountOnClose="true"
    class="drawer-add"
  >
    <div class="record-drawer-content">
      <a-spin
        :loading="loading"
        tip="保存中..."
      >
        <a-form
          :model="formData"
          :rules="rules"
          ref="formRef"
          layout="vertical"
        >
          <a-form-item
            field="groupName"
            label="集团名称"
            required
          >
            <a-input
              v-model="formData.groupName"
              allow-clear
              placeholder="请输入集团名称"
            />
          </a-form-item>
          <a-form-item
            field="promotionEntity"
            label="推广主体"
            required
          >
            <a-input
              v-model="formData.promotionEntity"
              allow-clear
              placeholder="请输入推广主体"
            />
          </a-form-item>
          <a-form-item
            field="theoreticalCost"
            label="理论消耗"
            required
          >
            <a-input-number
              v-model="formData.theoreticalCost"
              allow-clear
              placeholder="请输入理论消耗"
            />
          </a-form-item>
          <a-form-item
            field="sales"
            label="销售"
            required
          >
            <a-input
              v-model="formData.sales"
              allow-clear
              placeholder="请输入销售"
            />
          </a-form-item>
          <a-form-item label="是否置顶">
            <a-switch v-model="formData.sorting" :checked-value="'1'" :unchecked-value="'999'" checked-text="置顶"/>
          </a-form-item>
        </a-form>
        <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-end; ">
          <a-button
            type="primary"
            @click="submit"
            size="large"
          >保存</a-button>
        </div>
      </a-spin>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
    defineOptions({ name: 'DrawerAddFrame' });
    defineExpose({
        open
    });
    import { ref, reactive } from 'vue';
    import { isMobile } from '@/utils';
    import { Message } from '@arco-design/web-vue';
    import { addStressFrameApi, editStressFrameApi } from '../../../../apis/custTable';

    const visible = ref(false);
    const title = ref('新增');
    const loading = ref(false)
    const emit = defineEmits<{
      (e: 'refreshList'): void
    }>();
    let formData = reactive({
      id: '',
      groupName: '',
      promotionEntity: '',
      theoreticalCost: '',
      sales: '',
      sorting:""
    });
    // 新增mode变量保存当前模式
    let mode = ref<'add' | 'edit'>('add');
    const formRef = ref();
    const rules = {
        groupName: [
            { required: true, message: '请输入集团名称', trigger: 'blur' },
        ],
        promotionEntity: [
            { required: true, message: '请输入推广主体', trigger: 'blur' },
        ],
        theoreticalCost: [
            { required: true, message: '请输入理论消耗', trigger: 'blur' },
        ],
        sales: [
            { required: true, message: '请输入销售', trigger: 'blur' },
        ],
    };
    function open(type?: 'add' | 'edit', record?: any) {
        if (type === 'edit' && record) {
            mode.value = 'edit';
            title.value = '编辑';
            formData.id = record.id || '';
            formData.groupName = record.groupName || '';
            formData.promotionEntity = record.promotionEntity || '';
            formData.theoreticalCost = record.theoreticalCost || '';
            formData.sales = record.sales || '';
            formData.sorting = record.sorting || '999';
        } else {
            mode.value = 'add';
            title.value = '新增';
            formData.id = '';
            formData.groupName = '';
            formData.promotionEntity = '';
            formData.theoreticalCost = '';
            formData.sales = '';
            formData.sorting = '999';
        }
        visible.value = true;
    }
    function closePopup() {
        visible.value = false;
    }
    async function submit() {
        loading.value = true;
        try {
            const valid = await formRef.value?.validate();
            if (valid) {
                // Validation failed, exit early
                console.log('校验失败');
                return;
            }
            let res;
            if (mode.value === 'add') {
                res = await addStressFrameApi(formData);
            } else if (mode.value === 'edit') {
                res = await editStressFrameApi(formData);
            }
            if (res && res.code === '0') {
                Message.success(mode.value === 'add' ? '新增成功' : '编辑成功');
                visible.value = false;
                // 重置表单数据
                formData.id = '';
                formData.groupName = '';
                formData.promotionEntity = '';
                formData.theoreticalCost = '';
                formData.sales = '';
                emit('refreshList');
            } else {
                Message.error(res && res.msg ? res.msg : '请求失败');
            }
        } catch (err) {
            // 校验失败会进入catch块，不会执行API请求
            if (err && err.errors) {
                Message.error('请正确填写表单信息');
            } else {
                Message.error('请求失败，请稍后重试');
            }
        } finally {
            loading.value = false;
        }
    }
</script>
<style scoped>
    .record-drawer-content{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
    }
</style>