<template>
  <a-drawer
    v-model:visible="visible"
    :title="title"
    :width="isMobile() ? '100%' : 600"
    :footer="false"
    @close="closePopup"
    :unmountOnClose="true"
    class="drawer-add"
  >
    <div class="record-drawer-content">
      <a-spin
        :loading="loading"
        tip="保存中..."
      >
        <a-form
          :model="formData"
          :rules="rules"
          ref="formRef"
          layout="vertical"
        >
          <a-form-item
            field="customerName"
            label="广告主名称"
            required
          >
            <a-input
              v-model="formData.customerName"
              allow-clear
              placeholder="请输入广告主名称"
            />
          </a-form-item>
          <a-form-item
            field="sale"
            label="所属销售"
            required
          >
            <a-input
              v-model="formData.sale"
              allow-clear
              placeholder="请输入所属销售"
            />
          </a-form-item>
          <a-form-item
            field="target"
            label="QTD业绩"
            required
            v-if="currentMode === '增速' || currentMode === '体量'"
          >
            <a-input
              v-model="formData.target"
              allow-clear
              placeholder="请输入QTD业绩"
            />
          </a-form-item>
        </a-form>
        <div style="display: flex;flex-direction: row;align-items: center;justify-content: flex-end; ">
          <a-button
            type="primary"
            @click="submit"
            size="large"
          >保存</a-button>
        </div>
      </a-spin>
    </div>
  </a-drawer>
</template>
<script lang="ts" setup>
    defineOptions({ name: 'DrawerAdd' });
    defineExpose({
        open
    });
    import { isMobile } from '@/utils';
    import { Message } from '@arco-design/web-vue';
    import { addAnyCustmerApi } from '../../../../apis/custTable';
    const visible = ref(false);
    const title = ref('新增');
    const loading = ref(false)
    const currentMode = ref<string | undefined>();
    let formData = reactive({
      customerName: '',
      sale: '',
      target: '',
      type:currentMode.value,
    });
    const formRef = ref();
    const rules = {
        customerName: [
            { required: true, message: '请输入广告主名称', trigger: 'blur' },
        ],
        sale: [
            { required: true, message: '请输入所属销售', trigger: 'blur' },
        ],
        target: [
            { required: true, message: '请输入QTD业绩', trigger: 'blur' },
        ],
    };


    function open(options?: { mode?: string }) {
      if (options?.mode) {
        currentMode.value = options.mode;
      }
      visible.value = true;
    }
    const emit = defineEmits<{
      (e: 'refreshList'): void
    }>();
    async function submit() {
      loading.value = true

      try {
        // 校验表单，如果校验失败会抛出错误
        const valid = await formRef.value?.validate();
        if (valid) {
            // Validation failed, exit early
            console.log('校验失败');
            return;
        }
        // 只有校验通过才会执行到这里
        const params: any = {
          customerName: formData.customerName,
          sale: formData.sale,
          type: currentMode.value,
        };
        if (currentMode.value !== '开发') {
          params.target = formData.target;
        }
        const res = await addAnyCustmerApi(params)
        if (res.code === '0') {
          Message.success('新增成功')
          visible.value = false
          // 重置表单数据
          formData = {
            customerName: '',
            sale: '',
            target: '',
            type:currentMode.value,
          }
          emit('refreshList')
        } else {
          Message.error(res.msg)
        }
      } catch (err) {
        // 校验失败会进入catch块，不会执行API请求
        if (err && err.errors) {
          Message.error('请正确填写表单信息')
        } else {
          Message.error('请求失败，请稍后重试')
        }
      } finally {
        loading.value = false
      }
    }
    function closePopup() {
        visible.value = false;
    }
</script>
<style scoped>
  .record-drawer-content{
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
  }
</style>