<template>
    <div>
        <div>
            <a-button type="primary" @click="handleAdd">新增</a-button>
        </div>
         <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="false"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
            <template #action="{ record }">
                <a-button type="text" size="mini" @click="() => handleView(record)">编辑</a-button>
                <a-button type="text" status="danger" size="mini" @click="() => handleDelete(record)">删除</a-button>
            </template>
        </GiTable>
        <drawerAdd ref="drawerAddRef" @refreshList="fetchList"/>
        <deawerEide ref="deawerEideRef" @refreshList="fetchList"/>
    </div>
</template>
<script lang="ts" setup>
import { deleteAnyCustmerApi, getCustDevelopApi } from '@/apis/custTable';
import { onMounted } from 'vue';
import drawerAdd from './drawerAdd.vue'
import deawerEide from './deawerEide.vue'
import { ref } from 'vue';
import { Modal } from '@arco-design/web-vue'
import { Message } from '@arco-design/web-vue';
    defineOptions({ name: 'Develop' });
    const drawerAddRef = ref();
    const deawerEideRef = ref();
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'customerName', title: '广告主名称', width: 130, align: 'center' },
        { dataIndex: 'sale', title: '所属销售', width: 130, align: 'center' },
        // { dataIndex: 'qbtPerformance', title: 'QTD业绩', width: 130, align: 'center' },
        { dataIndex: 'action', title: '操作', slotName: 'action', width: 130, align: 'center' },
    ]
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    function handleView(record){
        deawerEideRef.value?.open({ mode: '开发', record })
        console.log(record)
    }
    function handleDelete(record){
         Modal.confirm({
          title: '确认删除',
          content: `是否确认删除账户：${record.customerName}`,
          okText: '确认',
          cancelText: '取消',
          titleAlign:'center',
          bodyStyle: {
            fontSize: '14px',
            lineHeight: '1.6',
            textAlign: 'center',
          },
          onOk: async () => {
            // 删除逻辑
            const res = await deleteAnyCustmerApi(record.id);
            if (res.code === '0') {
              Message.success('删除成功')
              fetchList()
            } else {
              Message.error(res.msg)
            }
          }
        });
        console.log(record)
    }
   async function getCustDevelop() {
      loading.value = true;
      try {
        const res = await getCustDevelopApi(1);
        if (res?.code === '0') {
          dataList.value = res.data || [];
        } else {
          dataList.value = [];
          console.error('获取开发客户列表失败:', res.msg);
        }
      } catch (err) {
        dataList.value = [];
        console.error('获取开发客户列表异常:', err);
      } finally {
        loading.value = false;
      }
    }
    function handleAdd(){
        drawerAddRef.value?.open({ mode: '开发' })
    }
    function fetchList(){
        getCustDevelop()
    }
    onMounted(()=>{
        getCustDevelop()
    })
</script>
<style>

</style>