<template>
    <a-card>
        <div>
            <a-button type="primary" @click="handleAddCust('add','')">新增</a-button>
        </div>
        <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="pagination"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
            @page-change="handlePageChange"
            @page-size-change="handlePageSizeChange"
        >
            <template #action="{ record }">
                <a-button type="text" size="mini" @click="() => handleAddCust('edit',record)">编辑</a-button>
                <a-button type="text" status="danger" size="mini" @click="() => handleDelete(record)">删除</a-button>
            </template>
        </GiTable>
        <!-- 新增/编辑弹窗 -->
        <a-modal :width="isMobile()?'80%':'500px'" v-model:visible="visible" @cancel="handleCancel" :footer="false">
            <template #title>
                {{ titles }}
            </template>
            <a-form :model="formData" :rules="rules" ref="formRef" layout="vertical">
                <a-form-item field="promotionEntity" label="推广主体" required>
                    <a-input  v-model="formData.promotionEntity" allow-clear placeholder="请输入推广主体" />
                </a-form-item>
            </a-form>
            <div style="text-align: end;">
                <a-button type="primary" @click="handleOk" size="large">保存</a-button>
            </div>
        </a-modal>

    </a-card>
</template>
<script lang="ts" setup>
    defineOptions({ name: 'NotShare' });
    import { ref } from 'vue';
    import { addNotShareApi, deleteNotShareApi, editNotShareApi, getNotShareApi } from '@/apis/custTable';
    import { onMounted } from 'vue';
    import { Message, Modal } from '@arco-design/web-vue';
    import { isMobile } from '@/utils';
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'id', title: 'ID', width: 130, align: 'center' },
        { dataIndex: 'promotionEntity', title: '推广主体', width: 130, align: 'center' },
        { dataIndex: 'action', title: '操作', slotName: 'action', width: 130, align: 'center' },
    ]
    const pagination = reactive({
        currentPage: 1,           // 当前页
        pageSize: isMobile() ? 15 : 10,         // 每页条数
        total: 0,             // 总数（在接口返回后更新）
        showTotal: true,      // 显示总数
        showPageSize: isMobile() ? false : true,   // 显示每页条数选择器
        showJumper: true      // 显示跳转页码输入框
    })
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    const titles = ref('新增');
    const visible = ref(false);
    // 统一用于新增/编辑的表单数据
    const formData = reactive({
        promotionEntity: '',
        id: ''
    });
    const rules = {
        promotionEntity: [
            { required: true, message: '请输入推广主体', trigger: 'blur' },
        ],
    };
    function handleDelete(record){
        Modal.confirm({
          title: '确认删除',
          content: `是否确认删除账户：${record.promotionEntity}`,
          okText: '确认',
          cancelText: '取消',
          titleAlign:'center',
          bodyStyle: {
            fontSize: '14px',
            lineHeight: '1.6',
            textAlign: 'center',
          },
          onOk: async () => {
            // 删除逻辑
            const res = await deleteNotShareApi(record.id);
            if (res.code === '0') {
              Message.success('删除成功')
              getCustDevelop()
            } else {
              Message.error(res.msg)
            }
          }
        });
        console.log(record)
    }
    async function getCustDevelop(){
        loading.value = true;
        const params = {
            page: pagination.currentPage,
            pageSize: pagination.pageSize,
        };
        const res = await getNotShareApi(params);
        dataList.value = res.data.records;
        pagination.total = res.data.total;
        loading.value = false;
    }
    const formRef = ref();
    const mode = ref('');
    function handleAddCust(type, record) {
        mode.value = type;
        if (type === 'add') {
            titles.value = '新增';
            formData.promotionEntity = '';
            formData.id = '';
        } else if (type === 'edit') {
            titles.value = '编辑';
            formData.promotionEntity = record.promotionEntity;
            formData.id = record.id;
        }
        visible.value = true;
    }

    async function handleOk() {
        try {
            const valid = await formRef.value?.validate();
            if (valid) {
                console.log('校验失败', valid);
                return;
            }
            let res;
            if (mode.value === 'add') {
                res = await addNotShareApi({ promotionEntity: formData.promotionEntity });
            } else if (mode.value === 'edit') {
                res = await editNotShareApi({ promotionEntity: formData.promotionEntity, id: formData.id });
            }
            if (res && res.code === '0') {
                Message.success(mode.value === 'add' ? '新增成功' : '编辑成功');
                visible.value = false;
                getCustDevelop();
            } else if (res) {
                Message.error(res.msg);
            }
            console.log('校验成功', valid);
        } catch (err) {
            if (err && err.errors) {
                Message.error('请正确填写表单信息');
            } else {
                Message.error('请求失败，请稍后重试');
            }
        }
    }
    function handleCancel(){
        visible.value = false;
    }
    // 点击下一页事件
function handlePageChange(page){
    console.log(page,'page')
    pagination.currentPage = page
    getCustDevelop()
}

// 每页条数改变事件
function handlePageSizeChange(pageSize){
    console.log(pageSize,'pageSize')
    pagination.pageSize = pageSize
    getCustDevelop()
}
    onMounted(()=>{
        getCustDevelop()
    })
</script>
<style>

</style>