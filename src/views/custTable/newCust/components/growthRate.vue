<template>
    <div>
        <div>
            <a-button type="primary" @click="handleAdd">新增</a-button>
        </div>
         <GiTable
            :data="dataList"
            :columns="columns"
            :loading="loading"
            :pagination="false"
            :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
            row-key="key"
            v-model:selectedKeys="selectedKeys"
            :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
            <template #cell-target="{ record }">
                {{ formatNumber(record.target) }}
            </template>
            <template #action="{ record }">
                <a-button type="text" size="mini" @click="() => handleView(record)">编辑</a-button>
                <a-button type="text" status="danger" size="mini" @click="() => handleDelete(record)">删除</a-button>
            </template>
        </GiTable>
        <drawerAdd ref="drawerAddRef" @refreshList="fetchList"/>
        <deawerEide ref="deawerEideRef" @refreshList="fetchList"/>
    </div>
</template>
<script lang="ts" setup>
import { deleteAnyCustmerApi, getCustDevelopApi } from '@/apis/custTable';
import { onMounted } from 'vue';
import drawerAdd from './drawerAdd.vue'
import deawerEide from './deawerEide.vue'
import { Message, Modal } from '@arco-design/web-vue';
import { formatNumber } from '@/utils/formatNumber';
    defineOptions({ name: 'GrowthRate' });
    const dataList = ref([]);
    const columns = [
        { dataIndex: 'customerName', title: '广告主名称', width: 130, align: 'center' },
        { dataIndex: 'sale', title: '所属销售', width: 130, align: 'center' },
        { dataIndex: 'target', title: 'QTD业绩', width: 130, align: 'center',slotName:'cell-target' },
        { dataIndex: 'action', title: '操作', slotName: 'action', width: 130, align: 'center' },
    ]
    // 千分符号
    // function formatNumber(value: number | string) {
    //     if (value == null || value === '') return '';
    //     const num = Number(value);
    //     if (i   sNaN(num)) return value;
    //     return num.toLocaleString();
    // }
    const loading = ref(false);
    const selectedKeys = ref([]); // 选中行的key
    const deawerEideRef = ref();
    function handleView(record){
        deawerEideRef.value?.open({ mode: '增速', record })
        console.log(record)
    }
    function handleDelete(record){
        Modal.confirm({
          title: '确认删除',
          content: `是否确认删除账户：${record.customerName}`,
          okText: '确认',
          cancelText: '取消',
          titleAlign:'center',
          bodyStyle: {
            fontSize: '14px',
            lineHeight: '1.6',
            textAlign: 'center',
          },
          onOk: async () => {
            // 删除逻辑
            const res = await deleteAnyCustmerApi(record.id);
            if (res.code === '0') {
              Message.success('删除成功')
              fetchList()
            } else {
              Message.error(res.msg)
            }
          }
        });
        console.log(record)
    }
    async function getCustDevelop(){
        loading.value = true;
        const res = await getCustDevelopApi(2);
        dataList.value = res.data;
        loading.value = false;
    }
    const drawerAddRef = ref();
    function handleAdd(){
        drawerAddRef.value?.open({ mode: '增速' })
    }
    function fetchList(){
        getCustDevelop()
    }
    onMounted(()=>{
        getCustDevelop()
    })
</script>
<style>

</style>