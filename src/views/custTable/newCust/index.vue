<template>
    <div class="gi_page">
        <a-tabs
            size="large"
            default-active-key="1"
            @change="change"
        >
            <a-tab-pane
                key="1"
                title="新客配置"
            >
                <NewCustmoer/>
            </a-tab-pane>
            <a-tab-pane
                key="2"
                title="非共服客户"
            >
                <NotShare/>
            </a-tab-pane>
            <a-tab-pane
                key="3"
                title="重点框架配置"
            >
                <StressFrame/>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>
<script lang="ts" setup>
    defineOptions({ name: 'NewCust' });
    import NewCustmoer from './components/newCustmoer.vue'
    import NotShare from './components/notShare.vue'
    import StressFrame from './components/stressFrame.vue'
    function change(key: string) {
        console.log(key)
    }
</script>
<style>

</style>