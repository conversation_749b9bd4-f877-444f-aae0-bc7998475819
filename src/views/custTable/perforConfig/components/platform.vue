<template>

    <a-card
      title="百度文鸾配置"
    >
      <template #extra>
        <a-button
          type="primary"
          @click="submitPerforConfig"
        >提交</a-button>
      </template>
      <div class="filter-box">
        <div>
          <p class="title">任务数</p>
          <a-input
            v-model="formData.taskNum"
            placeholder="请输入任务数"
            allow-clear
          >
            <template #append>个</template>
          </a-input>
        </div>
        <div>
          <p class="title">第一任务完成率</p>
          <a-input
            v-model="formData.phase1Completion"
            placeholder="请输入第一任务完成率"
            allow-clear
          >
            <template #append>%</template>
          </a-input>
        </div>
        <div>
          <p class="title">第二任务完成率</p>
          <a-input
            v-model="formData.phase2Completion"
            placeholder="请输入第二任务完成率"
            allow-clear
          >
            <template #append>%</template>
          </a-input>
        </div>
        <div>
          <p class="title">第三任务完成率</p>
          <a-input
            v-model="formData.phase3Completion"
            placeholder="请输入第三任务完成率"
            allow-clear
          >
            <template #append>%</template>
          </a-input>
        </div>
      </div>
    </a-card>
</template>
<script lang="ts" setup>
import { getPerforConfigApi, submitPerforConfigApi } from '@/apis/custTable';
import { Message } from '@arco-design/web-vue';
import { ref, onMounted } from 'vue';

defineOptions({ name: 'PerforConfig' });
const initialFormData = ref({
  department:'百度文鸾配置',
  taskNum: '',
  phase1Completion: '',
  phase2Completion: '',
  phase3Completion: '',
});

const formData = ref({ ...initialFormData.value });

// 映射接口返回的数据到表单字段
function mapApiDataToForm(data: any) {
  return {
    department:'百度文鸾配置',
    taskNum: data.taskName || '',
    phase1Completion: data.phase1Completion || '',
    phase2Completion: data.phase2Completion || '',
    phase3Completion: data.phase3Completion || '',
  };
}


async function submitPerforConfig() {
  // 判断是否全为空
  const isEmpty = Object.values(formData.value).every(
    v => v === null || v === '' || v === undefined
  );
  if (isEmpty) {
    Message.warning('请填写数据');
    return;
  }

  // 判断是否修改过
  const isUnchanged = Object.keys(formData.value).every(
    key =>
      formData.value[key as keyof typeof formData.value] ===
      initialFormData.value[key as keyof typeof initialFormData.value]
  );
  if (isUnchanged) {
    Message.warning('数据未修改，不允许提交');
    return;
  }

  const res = await submitPerforConfigApi(formData.value);
  if (res.code === '0') {
    Message.success('提交成功');
    initialFormData.value = { ...formData.value };
  } else {
    Message.error(res.msg);
  }
}

async function getPerforConfig() {
  const res = await getPerforConfigApi();
  if (res.code === '0') {
    formData.value = mapApiDataToForm(res.data);
    initialFormData.value = { ...formData.value };
  } else {
    Message.error(res.msg);
  }
}
onMounted(() => {
  getPerforConfig();
});
</script>
<style  scoped>
.filter-box {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
}
.filter-box > div {
  width: 100%;
}
@media screen and (max-width: 680px) {
  .filter-box {
    grid-template-columns: repeat(1, 1fr);
    justify-items: center;
    width: 100%;
    > div {
      width: 100%;
    }
  }
}
.title{
    margin-bottom: 10px;
}
</style>