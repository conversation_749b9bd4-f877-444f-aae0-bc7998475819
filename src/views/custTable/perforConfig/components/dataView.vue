<template>
  <a-card title="任务配置">
    <GiTable
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: '100%', y: '100%', minWidth: 1500 }"
      :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
    >
      <template #cell-taskNum="{ record }">
        <template v-if="!record.editing">
          {{ formatNumber(record.taskNum) }}
          <IconEdit style="cursor: pointer;" @click="() => handleEdit(record)"/>
        </template>
        <template v-else>
          <a-input ref="taskInputRef" style="width: 150px;" v-model="record.taskNum" @blur="handleTaskNumChange(record)" />
        </template>
      </template>
    </GiTable>
  </a-card>

</template>
<script lang="ts" setup>
import { getPerforConfigApi, submitPerforConfigApi } from '@/apis/custTable';
import { Message } from '@arco-design/web-vue';
import { ref, onMounted, nextTick } from 'vue';
import { formatNumber } from '@/utils/formatNumber';
const dataList = ref([]);
const columns = [
  {
    dataIndex: 'region',
    title: '地区',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'department',
    title: '部门',
    width: 130,
    align: 'center',
  },
  {
    dataIndex: 'taskNum',
    slotName: 'cell-taskNum',
    title: '任务数',
    width: 130,
    align: 'center',
  },
];
const loading = ref(false);
const taskInputRef = ref();
async function getPerforConfig() {
  const res = await getPerforConfigApi();
  if (res.code === '0') {
    dataList.value = res.data.mediaTaskBm;
  } else {
    Message.error(res.msg);
  }
}
function handleEdit(record) {
  record._originalTaskNum = record.taskNum;
  record.editing = true;
  nextTick(() => { taskInputRef.value?.focus(); });
}
async function handleTaskNumChange(record) {
  if (record.taskNum === record._originalTaskNum) {
    record.editing = false;
    return;
  }
  // 拼接要传给后端的参数
  const params = {
    taskNum: record.taskNum,
    department: record.department,
    region: record.region
  };
  try {
    // 这里调用你对应的更新接口，比如 updatePerforConfigApi
    const res = await submitPerforConfigApi(params);
    if (res.code === '0') {
      Message.success('更新成功');
    } else {
      Message.error(res.msg);
    }
  } catch (err) {
    Message.error('更新失败');
  }
  record.editing = false;
}
onMounted(() => {
  getPerforConfig();
});
</script>