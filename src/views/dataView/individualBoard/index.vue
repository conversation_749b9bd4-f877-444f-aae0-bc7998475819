<template>
  <div class="red-box-module">
    <!-- 标题栏 -->
    <div class="header">
      <h2>个人数据面板</h2>

    </div>

    <!-- 统计卡片区域 -->
    <div class="stat-cards">
      <a-card class="stat-card" v-for="(card, index) in statCards" :key="index">
        <div class="stat-card-content">
          <div class="stat-card-title">{{ card.title }}</div>
          <div class="stat-card-value" v-animate-number="card.value "></div>
          <!-- <div class="stat-card-trend" :class="card.trend > 0 ? 'trend-up' : 'trend-down'">
            <span v-if="card.trend > 0">
              <icon-caret-up />
            </span>
            <span v-else>
              <icon-caret-down />
            </span>
            {{ Math.abs(card.trend) }}%
            <span class="trend-compare">较{{ card.comparePeriod }}</span>
          </div> -->
        </div>
        <div class="stat-card-icon" :style="{ backgroundColor: card.iconBgColor }">
        </div>
      </a-card>
    </div>

    <!-- 数据表格区域 - 使用封装的GiTable组件 -->
    <div class="table-container">
      <a-card>
        <div class="table-header">
          <h3>详细数据列表</h3>
        </div>

        <!-- 使用封装的GiTable组件 -->
        <GiTable
          :columns="tableColumns"
          :data="tableData"
          :loading="isTableLoading"
          :pagination="pagination"
          :disabled-tools="['refresh', 'size', 'setting', 'fullscreen']"
        >
          <template #cell-yesterdayCost="{ record }">
            {{ formatNumber(record.yesterdayCost) }}
          </template>
          <template #cell-monthlyCost="{ record }">
            {{ formatNumber(record.monthlyCost) }}
          </template>
        </GiTable>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDepartApi, getPersonApi } from '@/apis/dataView';
import { formatNumber } from '@/utils/formatNumber';
import { Message } from '@arco-design/web-vue';
import { ref, onMounted, computed } from 'vue';

// 定义统计卡片类型
interface StatCard {
  title: string;
  value: number;
  trend: number;
  comparePeriod: string;
  iconBgColor: string;
}

// 定义表格列类型
interface TableColumn {
  title: string;
  dataIndex: string;
  width?: number;
  minWidth?: number;
  ellipsis?: boolean;
  tooltip?: boolean;
  slotName?: string;
}

// 定义表格数据类型
interface TableItem {
  key: string;
  name: string;
  yesterdayCost: number;
  theDayBeforeYesterdayCost: number;
  costRatio: number;
  monthlyCost: number;
  children?: TableItem[];
}

// 状态定义
const isTableLoading = ref(false);
// 表格数据
const tableData = ref<TableItem[]>([])

// 统计卡片数据 - 改为两个：昨日总消耗和当月总消耗
const statCards = computed<StatCard[]>(() => [
  {
    title: "昨日总消耗",
    value: personalYesterdayCost.value,
    trend: 5.2,
    comparePeriod: "前日",
    iconBgColor: "rgba(72, 187, 120, 0.1)"
  },
  {
    title: "当月总消耗",
    value: personalMondayCostBoard.value,
    trend: 8.7,
    comparePeriod: "上月同期",
    iconBgColor: "rgba(24, 144, 255, 0.1)"
  }
]);

const tableColumns = [
    { title: '客户', dataIndex: 'name',  ellipsis: true, tooltip: true },
    { title: '昨日消耗', dataIndex: 'yesterdayCost',  ellipsis: true, tooltip: true ,slotName:'cell-yesterdayCost'},
    { title: '当月消耗', dataIndex: 'monthlyCost',  ellipsis: true, tooltip: true ,slotName:'cell-monthlyCost'},
]




// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
});

function mapDeptData(list: any[]): any[] {
  return list.map(item => {
    const children = item.list ? mapDeptData(item.list) : undefined

    const newItem = {
      ...item,
      children,
      isLeaf: !children || children.length === 0 // 没有子节点就标记为叶子
    }

    delete newItem.list // 去掉原字段，避免干扰
    return newItem
  })
}
const personalMondayCostBoard = ref(0)
const personalYesterdayCost = ref(0)

async function getDepartList() {
  isTableLoading.value = true;
  const res = await getPersonApi()
  if (res.code !== '0') {
    Message.error(res.msg || '获取数据失败')
    isTableLoading.value = false
    return
  }
  isTableLoading.value = false
  // 转换结构：deptDOS → children + isLeaf
  tableData.value = mapDeptData(res.data.customerList)
  personalMondayCostBoard.value = res.data.personalMondayCost
  personalYesterdayCost.value = res.data.personalYesterdayCost
  console.log(tableData.value, '转换后的树形数据')
  console.log(res.data.customerList[0], '第一条返回数据')
}

// 初始化数据
onMounted(() => {
  getDepartList();
});

</script>

<style scoped>
.red-box-module {
  padding: 20px;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-actions {
  display: flex;
  align-items: center;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  overflow: hidden;
  position: relative;
  padding: 16px;
}

.stat-card-content {
  display: flex;
  flex-direction: column;
  padding-right: 60px;
}

.stat-card-title {
  font-size: 14px;
  margin-bottom: 8px;
}

.stat-card-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.stat-card-trend {
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trend-up {
  color: #00b42a;
}

.trend-down {
  color: #f53f3f;
}

.trend-compare {
  color: #86909c;
  margin-left: 4px;
}

.stat-card-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.charts-container {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f2f3f5;
}

.chart-content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.chart-skeleton {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-line {
  width: 100%;
  height: 240px;
  background: linear-gradient(
    to bottom,
    rgba(24, 144, 255, 0.1) 0%,
    rgba(24, 144, 255, 0.05) 100%
  );
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.chart-line::after {
  content: '';
  position: absolute;
  top: 40%;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1890ff;
}

.table-container {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-actions {
  display: flex;
  align-items: center;
}
</style>
