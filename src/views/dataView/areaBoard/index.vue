<template>
  <div class="gi_page">
    <a-card>
      <div>
        <tempvView/>
      </div>

      <div style="margin-top: 50px;">
        <lineData />
      </div>
    </a-card>

  </div>
</template>
<script lang="ts" setup>
import lineData from './conponents/line.vue';
import tempvView from './conponents/tempvView.vue';
import { useAppStore } from '@/stores/useAppStore';
import { ref, computed } from 'vue';
const appStore = useAppStore();
const theme = computed(() => appStore.theme);
</script>
<style>
</style>