<template>
  <div
    class="dashboard-container"
    :class="theme"
  >
    <!-- 页面头部 -->
    <header class="dashboard-header">
      <div class="header-content">
        <h1>区域运营数据看板</h1>
        <!-- <p class="update-time">更新时间: 2023年08月26日</p> -->
      </div>

      <!-- 区域切换器 -->
      <div class="region-switcher">
        <a-radio-group
          v-model="activeRegion"
          type="radio"
          size="large"
          @change="handleRegionChange"
        >
          <a-radio
            :value="item.value"
            v-for="item in source"
            :key="item.value"
          >{{ item.area }}</a-radio>
        </a-radio-group>
      </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 区域总览卡片 -->
      <section
        class="overview-card"
        :class="activeRegion"
      >
        <div class="overview-header">
          <h2>{{ activeRegion === 'north' ? '华北区域' : '华东区域' }} - 08月总览</h2>
          <a-tag :color="activeRegion === 'north' ? '#165DFF' : '#3FB429'">本月数据</a-tag>
        </div>

        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-label">总任务目标</div>
            <div class="stat-value" :style="activeRegion==='north' ? {color: '#165DFF'} : {color: '#3FB429'}" v-animate-number="currentData.totalTask"></div>
            <div class="progress-bar">
              <a-progress
                :percent="currentData.progressRate"
                :stroke-width="8"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
            </div>
          </div>

          <div class="stat-item">
            <div class="stat-label">总消耗</div>
            <div class="stat-value highlight" v-animate-number="currentData.totalCost"></div>
          </div>
        </div>
      </section>

      <!-- 部门数据卡片 -->
      <section class="departments-grid">
        <!-- 营销一部 -->
        <div
          class="dept-card"
          :class="activeRegion"
        >
          <div class="dept-header">
            <h3>营销策略一部</h3>
          </div>

          <div class="dept-stats">
            <div class="dept-stat-item">
              <span class="stat-label">部门任务</span>
              <span class="stat-value">{{ currentData.dept1.task }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">当月消耗</span>
              <span class="stat-value">{{ currentData.dept1.monthCost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">昨日消耗</span>
              <span class="stat-value">{{ currentData.dept1.dayCost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">任务进度</span>
              <a-progress
                :percent="currentData.dept1.progress"
                :stroke-width="8"
                :style="{width: '150px'}"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
            </div>
          </div>
        </div>

        <!-- 营销二部 -->
        <div
          class="dept-card"
          :class="activeRegion"
        >
          <div class="dept-header">
            <h3>营销策略二部</h3>
          </div>

          <div class="dept-stats">
            <div class="dept-stat-item">
              <span class="stat-label">部门任务</span>
              <span class="stat-value">{{ currentData.dept2.task }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">当月消耗</span>
              <span class="stat-value">{{ currentData.dept2.monthUgcost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">昨日消耗</span>
              <span class="stat-value">{{ currentData.dept2.dayUgcost }}</span>
            </div>
            <div class="dept-stat-item">
              <span class="stat-label">任务进度</span>
              <a-progress
                :percent="currentData.dept2.progress "
                :stroke-width="8"
                :style="{width: '150px'}"
                :status="activeRegion==='north' ? 'normal' : 'success'"
              />
            </div>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useAppStore } from '@/stores/useAppStore';
import { useUserStore } from '@/stores/modules/user'
import { formatNumber } from '@/utils/formatNumber';
import { getAreaApi } from '@/apis/dataView';

// -------------------------- 1. 类型定义（确保TypeScript类型安全） --------------------------
/** 接口返回的单个区域（华东/华北）原始数据结构 */
interface AreaApiRawData {
  totalTask: number | string;
  totalCost: number | string;
  yesterdayCost: number | string;
  totalTaskRate: number | string;
  marketingDepartmentOneTask: number | string;
  marketingDepartmentOneCost: number | string;
  marketingDepartmentOneRate: number | string;
  marketingDepartmentOneYesterdayCost: number | string;
  marketingDepartmentTwoTask: number | string;
  marketingDepartmentTwoCost: number | string;
  marketingDepartmentTwoRate: number | string;
  marketingDepartmentTwoYesterdayCost: number | string;
}

/** 接口返回的完整data字段结构（hd=华东，hb=华北） */
interface ApiData {
  hd: AreaApiRawData;
  hb: AreaApiRawData;
}

/** 接口返回的整体结构 */
interface ApiResponse {
  code: string;
  msg: string;
  success: boolean;
  timestamp: number;
  data: ApiData;
}

/** 页面渲染所需的数据结构（与模板匹配） */
interface RegionData {
  totalTask: number;
  totalCost: number;
  progressRate: number; // 0-1之间的小数（对应百分比）
  dept1: {
    task: string;
    monthCost: string;
    dayCost: string;
    progress: number; // 0-1之间的小数
  };
  dept2: {
    task: string;
    monthUgcost: string;
    dayUgcost: string;
    progress: number; // 0-1之间的小数
  };
}

// -------------------------- 2. 状态管理 --------------------------
const userStore = useUserStore();
const appStore = useAppStore();

// 区域选择器数据源（华东/华北）
const source = ref<Array<{ area: string; value: 'east' | 'north' }>>([]);
// 当前选中的区域（east=华东，north=华北）
const activeRegion = ref<'east' | 'north'>('east');
// 存储接口返回的原始数据
const apiRawData = ref<ApiData>({
  hd: {} as AreaApiRawData,
  hb: {} as AreaApiRawData
});

// 主题切换（复用原有逻辑）
const theme = computed(() => appStore.theme);

// 区域与接口key的映射关系（页面区域值 → 接口数据key）
const regionKeyMap = {
  east: 'hd',
  north: 'hb'
} as const;

// -------------------------- 3. 接口请求与数据处理 --------------------------
/** 请求区域数据并存储 */
async function getAreaData() {
  try {
    const res = await getAreaApi();
    const response = res as ApiResponse;

    // 校验接口返回是否成功
    if (response.success && response.code === '0') {
      apiRawData.value = response.data;
      console.log('接口数据加载成功：', apiRawData.value);
    } else {
      console.error('接口返回失败：', response.msg);
    }
  } catch (error) {
    console.error('接口调用异常：', error);
  }
}

/** 区域切换事件（复用原有逻辑） */
function handleRegionChange() {
  console.log('当前选中区域：', activeRegion.value);
}

// -------------------------- 4. 计算属性（适配页面渲染数据） --------------------------
const currentData = computed<RegionData>(() => {
  // 1. 获取当前区域对应的接口数据key（east→hd，north→hb）
  const targetKey = regionKeyMap[activeRegion.value];
  // 2. 获取原始数据（兜底空对象避免报错）
  const rawData = apiRawData.value[targetKey] || ({} as AreaApiRawData);

  // 工具函数：格式化金额（转数字→加¥符号→千分位→保留2位小数）
  const formatMoney = (val: number | string | undefined) => {
    const num = Number(val || 0); // 空值/非数字默认0
    return `¥${formatNumber(num, 2)}`; // 依赖项目的formatNumber工具
  };

  // 工具函数：格式化进度（转数字→限制0-1区间，避免负数/超100%）
  const formatProgress = (val: number | string | undefined) => {
    const rate = Number(val || 0);
    return Math.max(0, Math.min(1, rate)); // 进度条安全值（0≤x≤1）
  };

  // 3. 转换为页面所需结构
  return {
    totalTask: Number(rawData.totalTask || 0), // 动画需要纯数字
    totalCost: Number(rawData.totalCost || 0), // 动画需要纯数字
    progressRate: formatProgress(rawData.totalTaskRate),
    dept1: {
      task: formatMoney(rawData.marketingDepartmentOneTask),
      monthCost: formatMoney(rawData.marketingDepartmentOneCost),
      dayCost: formatMoney(rawData.marketingDepartmentOneYesterdayCost),
      progress: formatProgress(rawData.marketingDepartmentOneRate)
    },
    dept2: {
      task: formatMoney(rawData.marketingDepartmentTwoTask),
      monthUgcost: formatMoney(rawData.marketingDepartmentTwoCost), // 匹配模板字段名
      dayUgcost: formatMoney(rawData.marketingDepartmentTwoYesterdayCost), // 匹配模板字段名
      progress: formatProgress(rawData.marketingDepartmentTwoRate)
    }
  };
});

// -------------------------- 5. 初始化逻辑（合并onMounted避免重复执行） --------------------------
onMounted(async () => {
  // 第一步：先获取用户信息，初始化区域选择器
  await userStore.getInfo();
  const deptPath = userStore.userInfo.deptPathName || '';

  if (deptPath.includes('华东')) {
    activeRegion.value = 'east';
    source.value = [{ area: '华东区域', value: 'east' }];
  } else if (deptPath.includes('华北')) {
    activeRegion.value = 'north';
    source.value = [{ area: '华北区域', value: 'north' }];
  } else {
    // 无区域权限时显示全部区域
    source.value = [
      { area: '华东区域', value: 'east' },
      { area: '华北区域', value: 'north' }
    ];
  }

  // 第二步：获取接口数据（依赖区域初始化）
  await getAreaData();
});
</script>

<style scoped>
/* 基础样式 */
.dashboard-container {
  /* min-height: 100vh; */
}

/* 亮色主题 */
.light {
  /* background-color: #f5f7fa; */
}

/* 暗色主题 */
.dark {
  background-color: #1d1e23;
  color: #f2f3f5;
}

/* 头部样式 */
.dashboard-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.light .header-content h1 {
  color: #1d2129;
}

.dark .header-content h1 {
  color: #ffffff;
}

.update-time {
  margin-top: 10px;
  font-size: 13px;
  transition: color 0.3s ease;
}

.light .update-time {
  color: #86909c;
}

.dark .update-time {
  color: #c9cdd4;
}

.region-switcher {
  /* width: 100%; */
}

/* 主内容区 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 总览卡片 */
.overview-card {
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-top: 3px solid;
}

.light .overview-card {
  background-color: #ffffff;
}

.dark .overview-card {
  background-color: #27282f;
}

.overview-card.north {
  border-top-color: #165dff;
}

.overview-card.east {
  border-top-color: #40b429;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.overview-header h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.light .overview-header h2 {
  color: #1d2129;
}

.dark .overview-header h2 {
  color: #ffffff;
}

.overview-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  padding: 14px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.light .stat-item {
  background-color: #f9fafb;
}

.dark .stat-item {
  background-color: #303138;
}

.stat-label {
  font-size: 13px;
  margin-bottom: 8px;
  margin-right: 10px;
  transition: color 0.3s ease;
}

.light .stat-label {
  color: #86909c;
}

.dark .stat-label {
  color: #c9cdd4;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

.light .stat-value {
  color: #1d2129;
}

.dark .stat-value {
  color: #f2f3f5;
}

.stat-value.highlight {
  font-weight: 700;
}

.overview-card.north .stat-value.highlight {
  color: #165dff;
}

.overview-card.east .stat-value.highlight {
  color: #40b429;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-text {
  font-size: 12px;
  text-align: right;
  transition: color 0.3s ease;
}

.light .progress-text {
  color: #86909c;
}

.dark .progress-text {
  color: #c9cdd4;
}

.growth-indicator {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #00b42a;
}

.trend-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 部门卡片网格 */
.departments-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 部门卡片 */
.dept-card {
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.light .dept-card {
  background-color: #ffffff;
}

.dark .dept-card {
  background-color: #27282f;
}

.dept-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dept-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.light .dept-header h3 {
  color: #1d2129;
}

.dark .dept-header h3 {
  color: #ffffff;
}

.dept-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.dept-stat-item {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.light .dept-stat-item {
  background-color: #f9fafb;
}

.dark .dept-stat-item {
  background-color: #303138;
}

.dept-stat-item .stat-value {
  font-size: 15px;
  margin-bottom: 0;
}

.dept-stat-item .stat-value.progress {
  font-weight: 700;
}

.dept-card.north .stat-value.progress {
  color: #165dff;
}

.dept-card.east .stat-value.progress {
  color: #40b429;
}

/* 媒体查询 - 平板及以上设备 */
@media (min-width: 768px) {
  .dashboard-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .overview-stats {
    flex-direction: row;
  }

  .stat-item {
    flex: 1;
  }

  .departments-grid {
    flex-direction: row;
  }

  .dept-card {
    flex: 1;
  }

  .chart-container {
    height: 250px;
  }
}

/* 媒体查询 - 桌面设备 */
@media (min-width: 1024px) {
  .dashboard-container {
    /* max-width: 1200px; */
    margin: 0 auto;
    /* padding: 32px; */
  }

  .overview-card,
  .dept-card,
  .trend-card {
    padding: 20px;
  }

  .overview-header h2 {
    font-size: 18px;
  }

  .stat-value {
    font-size: 22px;
  }

  .chart-container {
    height: 300px;
  }
}
</style>