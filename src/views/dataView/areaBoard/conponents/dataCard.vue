<template>
  <div
    class="data-card-container"
    :class="`theme-${theme}`"
  >

    <!-- 卡片1：本周消耗 -->
    <div
      class="card card-primary"
      hover
    >
      <div class="card-header">
        <p class="card-title">昨日消耗</p>
        <i class="card-icon fa fa-line-chart"></i>
      </div>
      <div class="card-value">{{ formatNumber(5280.00) }} </div>
      <div class="card-footer">
        <div class="footer-item">
          <span class="label">前日消耗</span>
          <span class="value">4811.00</span>
        </div>
        <div class="footer-item trend-down">
          <span class="label">同比</span>
          <span class="value">18%</span>
        </div>
      </div>
    </div>

    <!-- 卡片2：本周消耗 -->
    <div
      class="card card-success"
      hover
    >
      <div class="card-header">
        <p class="card-title">本周消耗</p>
        <i class="card-icon fa fa-shopping-cart"></i>
      </div>
      <div class="card-value">{{ formatNumber(27646.00) }}</div>
      <div class="trend-indicator trend-up">同比上周 ↑31.7%</div>
      <div class="progress-container">
        <a-progress
          :percent="0.7"
          :stroke-width="6"
          :style="{width:'100%'}"
        />
      </div>
    </div>

    <!-- 卡片3：本月消耗 -->
    <div
      class="card card-info"
      hover
    >
      <div class="card-header">
        <p class="card-title">本月消耗</p>
        <i class="card-icon fa fa-calendar-check-o"></i>
      </div>
      <div class="card-value">{{ formatNumber(91646.00) }}</div>
      <div class="trend-indicator">完成 76%</div>
      <div class="progress-container">
        <a-progress
          :percent="0.9"
          :stroke-width="6"
          :style="{width:'100%'}"
        />
        <p class="target">目标金额：100000</p>
      </div>
    </div>

    <!-- 卡片4：Q3消耗 -->
    <div
      class="card card-warning"
      hover
    >
      <div class="card-header">
        <p class="card-title">Q3消耗</p>
        <i class="card-icon fa fa-bar-chart"></i>
      </div>
      <div class="card-value">{{ formatNumber(811146.00) }}</div>
      <div class="trend-indicator">完成 76%</div>
      <div class="progress-container">
        <a-progress
          :percent="1"
          :stroke-width="6"
          :style="{width:'100%'}"
        />
        <p class="target">目标金额：1000000</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
import { formatNumber } from '@/utils/formatNumber';
defineOptions({ name: 'DataCard' });

// 定义主题属性，支持dark和light，默认light
const props = defineProps({
  theme: {
    type: String,
    default: 'light',
    validator: (value: string) => {
      return ['dark', 'light'].includes(value);
    },
  },
});
</script>

<style lang="scss">
@use '@/styles/var.scss' as *;

// 主容器样式
.data-card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 24px;
  padding: 20px;
  transition: background-color 0.3s ease;
}

// 明亮主题
.theme-light {
  background-color: #fff;
}

// 暗黑主题
.theme-dark {
  background-color: #18181A;
}

// 卡片基础样式
// 卡片基础样式
.card {
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  width: calc(25% - 18px); // 考虑间距的宽度计算
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid #eaeaea; // 默认边框，主题覆盖

  // 卡片头部
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0;
      transition: color 0.3s ease;
    }

    .card-icon {
      font-size: 22px;
      opacity: 0.8;
    }
  }

  // 数值样式
  .card-value {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 16px 0;
    line-height: 1.2;
  }

  // 趋势指示器
  .trend-indicator {
    font-size: 14px;
    margin-bottom: 16px;
    padding-left: 2px;
    transition: color 0.3s ease;
  }

  // 进度条容器
  .progress-container {
    margin-bottom: 16px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .target {
      font-size: 12px;
      margin: 8px 0 0 0;
      text-align: right;
      transition: color 0.3s ease;
    }
  }

  // 卡片底部
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    padding-top: 12px;
    border-top: 1px solid #f5f5f5;
    transition: border-color 0.3s ease;

    .label {
      transition: color 0.3s ease;
      margin-right: 20px;

    }

    .value {
      font-weight: 500;
      transition: color 0.3s ease;
      color: #52c41a;
    }

    &.rank {
      justify-content: space-between;
    }
  }

  // 底部项目样式
  .footer-item {
    display: flex;
    flex-direction: row;
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 卡片悬停效果
.card[hover]:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

// 卡片颜色主题 - 明亮模式
.theme-light {
  .card {
    background: #fff;
    border: 1px solid #eaeaea;
    transition: border-color 0.3s;

    .card-footer {
      border-top-color: #f5f5f5;
    }

    .card-header .card-title,
    .card-footer .label {
      color: #666;
    }

    .progress-container .target {
      color: #888;
    }
  }
}

// 卡片颜色主题 - 暗黑模式
.theme-dark {
  .card {
    background: #2c2c2c;
    border: 1px solid #3a3a3a;
    transition: border-color 0.3s;

    .card-footer {
      border-top-color: #3a3a3a;
    }

    .card-header .card-title,
    .card-footer .label {
      color: #ccc;
    }

    .progress-container .target {
      color: #aaa;
    }
  }
}

// 卡片颜色主题
.card-primary {
  // border-top: 3px solid #1890ff;

  .card-icon,
  .card-value {
    color: #1890ff;
  }
}

.card-success {
  // border-top: 3px solid #52c41a;

  .card-icon,
  .card-value {
    color: #52c41a;
  }
}

.card-info {
  // border-top: 3px solid #40a9ff;

  .card-icon,
  .card-value {
    color: #40a9ff;
  }
}

.card-warning {
  // border-top: 3px solid #faad14;

  .card-icon,
  .card-value {
    color: #faad14;
  }
}

// 趋势样式
.trend-up {
  color: #f5222d;
  font-weight: 500;
}

.trend-down {
  color: #52c41a;
  font-weight: 500;
}

// 高亮样式
.highlight {
  color: #f5222d;
  font-weight: 700;
}

// 响应式布局
@media (max-width: 1024px) {
  .card {
    width: calc(50% - 12px); // 平板横向两块
  }
}

@media (max-width: 768px) {
  .data-card-container {
    gap: 16px;
    padding: 10px;
  }

  .card {
    width: 100%; // 手机横向一块
    padding: 18px;
  }

  .card-value {
    font-size: 24px;
  }
}

// 小屏手机优化
@media (max-width: 480px) {
  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .footer-item {
      margin-right: 0;
      width: 100%;
    }
  }
}
</style>
