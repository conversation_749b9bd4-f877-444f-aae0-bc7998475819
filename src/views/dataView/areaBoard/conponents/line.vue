<template>
  <div class=" baidu-trend ">
    <h2>消耗数据</h2>
    <div class="chart-container">
      <div ref="trendChart" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { useAppStore } from '@/stores/useAppStore'
import { getLineApi } from '@/apis/dataView'
import { useUserStore } from '@/stores/modules/user'
const userStore = useUserStore()
const appStore = useAppStore()
// 读取主题状态
const theme = computed(() => appStore.theme)
defineOptions({ name: 'lineData' })
const trendChart = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const dateList = ref<string[]>([])
const seriesList = ref<any[]>([])
async function getLineData() {
  const res = await getLineApi()
  dateList.value = res.data.date || []

  const deptPath = userStore.userInfo.deptPathName || ''
  let targetSeries: any[] = []

if (deptPath.includes('华东')) {
  targetSeries.push(...(res.data.seriesHd || []))
}

if (deptPath.includes('华北')) {
  targetSeries.push(...(res.data.seriesHb || []))
}

  const colorMap: Record<string, string> = {
    '营销一部-华东': '#5470C6',
    '营销二部-华东': '#91CC75',
    '营销一部-华北': '#EE6666',
    '营销二部-华北': '#FAC858'
  }

  seriesList.value = targetSeries.map((item: any) => {
    const region = deptPath.includes('华东') ? '华东' : '华北'
    const name = `${item.deptName}-${region}`

    // 补齐数据长度，缺失的用 null
    const data = [...item.costs]
    while(data.length < dateList.value.length) data.push(null)

    return {
      name,
      type: 'line',
      data,
      itemStyle: { color: colorMap[name] || '#5470C6' }
    }
  })

  initChart()
}
function getChartOption(themeValue: string) {
  return {
    xAxis: {
      type: 'category',
      data: dateList.value
    },
    yAxis: {
      type: 'value'
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '3%',  // 右边留点空，避免图例覆盖
    },
    series: seriesList.value,
    legend: {
      data: seriesList.value.map(s => s.name),
      top: 'bottom',
      left: 'center',
      textStyle: {
        color: themeValue === 'dark' ? '#fff' : '#000'
      }
    },
    // dataZoom: [
    //   { type: 'slider', start: 0, end: 100 },
    //   { type: 'inside', start: 0, end: 100 }
    // ]
  }
}
function initChart() {
  if (trendChart.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(trendChart.value, theme.value === 'dark' ? '' : 'light')
    const option = getChartOption(theme.value)
    chartInstance.setOption(option)
  }
}
onMounted(async() => {
  await userStore.getInfo();
  const deptPath = userStore.userInfo.deptPathName || ''
  console.log(deptPath, 'deptPath')
  getLineData()
})
watch(theme, () => {
  initChart()
})
</script>

<style scoped lang="scss">
.baidu-trend {
//   background-color: #fff;
    border-radius: 8px;

  h2 {
    margin-bottom: 16px;
    font-weight: 600;
    // color: #333;
  }

  .chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    // background-color: #000;
  }
}
</style>
