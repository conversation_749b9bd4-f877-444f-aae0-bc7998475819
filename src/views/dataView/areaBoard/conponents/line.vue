<template>
  <div class=" baidu-trend ">
    <h2>消耗数据</h2>
    <div class="chart-container">
      <div ref="trendChart" style="width: 100%; height: 100%;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from 'vue'
import * as echarts from 'echarts'
import { useAppStore } from '@/stores/useAppStore'
import { getLineApi } from '@/apis/dataView'
const appStore = useAppStore()
// 读取主题状态
const theme = computed(() => appStore.theme)
defineOptions({ name: 'lineData' })
const trendChart = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const dateList = ref<string[]>([])
const seriesList = ref<any[]>([])
async function getLineData() {
  const res = await getLineApi()
  dateList.value = res.data.date

  // 合并 seriesHd 和 seriesHb，并为每条线指定不同颜色
  // 颜色映射
  const colorMap: Record<string, string> = {
    '营销一部-华东': '#5470C6',
    '营销二部-华东': '#91CC75',
    '营销一部-华北': '#EE6666',
    '营销二部-华北': '#FAC858'
  }
  const hdSeries = (res.data.seriesHd || []).map((item: any) => {
    const name = `${item.deptName}-华东`
    return {
      name,
      type: 'line',
      data: item.costs,
      itemStyle: { color: colorMap[name] || '#5470C6' }
    }
  })
  const hbSeries = (res.data.seriesHb || []).map((item: any) => {
    const name = `${item.deptName}-华北`
    return {
      name,
      type: 'line',
      data: item.costs,
      itemStyle: { color: colorMap[name] || '#EE6666' }
    }
  })
  seriesList.value = [...hdSeries, ...hbSeries]
  initChart()
}
function getChartOption(themeValue: string) {
  return {
    xAxis: {
      type: 'category',
      data: dateList.value
    },
    yAxis: {
      type: 'value'
    },
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '3%',  // 右边留点空，避免图例覆盖
    },
    series: seriesList.value,
    legend: {
      data: seriesList.value.map(s => s.name),
      top: 'bottom',
      left: 'center',
      textStyle: {
        color: themeValue === 'dark' ? '#fff' : '#000'
      }
    },
    // dataZoom: [
    //   { type: 'slider', start: 0, end: 100 },
    //   { type: 'inside', start: 0, end: 100 }
    // ]
  }
}
function initChart() {
  if (trendChart.value) {
    if (chartInstance) {
      chartInstance.dispose()
    }
    chartInstance = echarts.init(trendChart.value, theme.value === 'dark' ? '' : 'light')
    const option = getChartOption(theme.value)
    chartInstance.setOption(option)
  }
}
onMounted(() => {
  getLineData()
})
watch(theme, () => {
  initChart()
})
</script>

<style scoped lang="scss">
.baidu-trend {
//   background-color: #fff;
    border-radius: 8px;

  h2 {
    margin-bottom: 16px;
    font-weight: 600;
    // color: #333;
  }

  .chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    // background-color: #000;
  }
}
</style>
