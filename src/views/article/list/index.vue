<template >
  <div style="padding: 20px; height: 100vh; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none;">

    <div>测试页面1</div>
    <a-progress size="large" :percent="0.2" />

    <!-- 折线图 -->
    <!-- <v-chart :key="'line-' + chartKey" :theme="theme" :option="optionLine " style="margin: 20px 0; width: 100%; height: 400px; border-radius: 10px; overflow: hidden;" autoresize /> -->
    <LineChart
      :categories="['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']"
      :seriesData="[502320, 235049, 751620, 452156, 123548, 102947, 951420]"
      :smooth="true"
      :background="theme === 'dark' ? '#000' : '#fff'"
      :styleProps="{ margin: '20px 0', width: '100%', height: '400px', borderRadius: '10px', overflow: 'hidden' }"
    />

    <!--饼状图  -->
    <!-- <v-chart :key="'pie-' + chartKey"  :theme="theme" :option="optionPie" style="margin: 20px 0; width: 100%; height: 400px; border-radius: 10px; overflow: hidden;" autoresize /> -->
    <PieChart
      :seriesData="[
        { value: 1048, name: '搜索引擎' },
        { value: 735, name: '直接访问' },
        { value: 580, name: '邮件营销' }
      ]"
      :background="theme === 'dark' ? '#000' : '#fff'"
      :styleProps="{ margin: '20px 0', width: '100%', height: '400px', borderRadius: '10px', overflow: 'hidden' }"
    />

   <!-- 柱状图 -->
    <BarChart :categories="['2012', '2013', '2014', '2015', '2016']"
      :seriesData="[
        {name: 'Forest', data: [1320, 332, 301, 334, 390]},
        {name: 'Steppe', data: [220, 182, 191, 234, 290]},
        {name: 'Desert', data: [150, 232, 201, 154, 190]},
        {name: 'Wetland', data: [98, 77, 101, 99, 0]},
        {name: 'Tundra', data: [70, 82, 91, 94, 90]},
        {name: 'Taiga', data: [62, 82, 91, 94, 90]},
        {name: 'Grassland', data: [62, 82, 91, 94, 90]}
      ]"
      :labelConfig="{ rotate: 90, position: 'insideTopRight' }"
      chartType="bar" :seriesStyle="{ barWidth: 25 }" />
  </div>

</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue'
  import { useAppStore } from '@/stores/useAppStore'

  const appStore = useAppStore()
  const theme = computed(() => appStore.theme)
  const chartKey = ref(0)

  watch(theme, () => {
    chartKey.value++
  })

  // 折线图
  // const optionLine = ref({
  //   backgroundColor: theme.value === 'dark' ? '#000' : '#fff',
  //   // title: { text: '本周访问趋势',textStyle: { color: '#982442' } },
  //   tooltip: { trigger: 'axis' },
  //   xAxis: {
  //     type: 'category',
  //     data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
  //   },
  //   yAxis: { type: 'value' },
  //   series: [
  //     {
  //       data: [820, 932, 901, 934, 1290, 1330, 1320],
  //       type: 'line',
  //       smooth: true
  //     }
  //   ]
  // })
  // 饼状图
  // const optionPie = ref({
  //     backgroundColor: theme.value === 'dark' ? '#000' : '#fff',
  //     // title: {
  //     //   text: '产品线占比图',
  //     //   left: 'left'
  //     // },
  //     tooltip: {
  //       trigger: 'item'
  //     },
  //     legend: {
  //       orient: 'horizontal',
  //       bottom: '0',
  //       left: 'center'
  //     },
  //     series: [
  //       {
  //         name: '来源',
  //         type: 'pie',
  //         radius: '50%',
  //         data: [
  //           { value: 1048, name: '搜索引擎' },
  //           { value: 735, name: '直接访问' },
  //           { value: 580, name: '邮件营销' },
  //           { value: 484, name: '联盟广告' },
  //           { value: 300, name: '视频广告' }
  //         ],
  //         emphasis: {
  //           itemStyle: {
  //             shadowBlur: 10,
  //             shadowOffsetX: 0,
  //             shadowColor: 'rgba(0, 0, 0, 0.5)'
  //           }
  //         }
  //       }
  //     ]
  // })
    // import {  onMounted,ref } from 'vue';
    // import { getArticleList } from '@/utils/article'
    // const article = ref([]);
    // onMounted(async () => {
    // try {
    //     const data = await getArticleList({ page: 1, pageSize: 10 })
    //     articleList.value = data
    //     console.log('文章列表：', data)
    //   } catch (error) {
    //     console.error('获取文章失败：', error)
    //   }
    // })

  // 你后续可以在这里写 setup 脚本
</script>
<style scoped>
    div::-webkit-scrollbar {
      display: none;
    }
</style>