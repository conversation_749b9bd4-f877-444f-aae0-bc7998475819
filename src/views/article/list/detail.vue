<template>
  <div>测试页面</div>
</template>

<script setup lang="ts">
// import {  onMounted,ref } from 'vue';
//   import { getArticleList } from '@/utils/article'
//   const article = ref([]);
//   onMounted(async () => {
//   try {
//       const data = await getArticleList({ page: 1, pageSize: 10 })
//       articleList.value = data
//       console.log('文章列表：', data)
//     } catch (error) {
//       console.error('获取文章失败：', error)
//     }
//   })
// setup 脚本逻辑
</script>