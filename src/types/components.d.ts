/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Avatar: typeof import('./../components/Avatar/index.vue')['default']
    BarChart: typeof import('./../components/echartCom/BarChart/barChart.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    CellCopy: typeof import('./../components/CellCopy/index.vue')['default']
    Chart: typeof import('./../components/Chart/index.vue')['default']
    ColumnSetting: typeof import('./../components/GiTable/src/components/ColumnSetting.vue')['default']
    CronForm: typeof import('./../components/GenCron/CronForm/index.vue')['default']
    CronModal: typeof import('./../components/GenCron/CronModal/index.vue')['default']
    DateRangePicker: typeof import('./../components/DateRangePicker/index.vue')['default']
    DayForm: typeof import('./../components/GenCron/CronForm/component/day-form.vue')['default']
    FilePreview: typeof import('./../components/FilePreview/index.vue')['default']
    GiCellAvatar: typeof import('./../components/GiCell/GiCellAvatar.vue')['default']
    GiCellGender: typeof import('./../components/GiCell/GiCellGender.vue')['default']
    GiCellStatus: typeof import('./../components/GiCell/GiCellStatus.vue')['default']
    GiCellTag: typeof import('./../components/GiCell/GiCellTag.vue')['default']
    GiCellTags: typeof import('./../components/GiCell/GiCellTags.vue')['default']
    GiCodeView: typeof import('./../components/GiCodeView/index.vue')['default']
    GiDot: typeof import('./../components/GiDot/index.tsx')['default']
    GiEditTable: typeof import('./../components/GiEditTable/GiEditTable.vue')['default']
    GiFooter: typeof import('./../components/GiFooter/index.vue')['default']
    GiForm: typeof import('./../components/GiForm/src/GiForm.vue')['default']
    GiIconBox: typeof import('./../components/GiIconBox/index.vue')['default']
    GiIconSelector: typeof import('./../components/GiIconSelector/index.vue')['default']
    GiIframe: typeof import('./../components/GiIframe/index.vue')['default']
    GiOption: typeof import('./../components/GiOption/index.vue')['default']
    GiOptionItem: typeof import('./../components/GiOptionItem/index.vue')['default']
    GiPageLayout: typeof import('./../components/GiPageLayout/index.vue')['default']
    GiSpace: typeof import('./../components/GiSpace/index.vue')['default']
    GiSplitButton: typeof import('./../components/GiSplitButton/index.vue')['default']
    GiSplitPane: typeof import('./../components/GiSplitPane/index.vue')['default']
    GiSplitPaneFlexibleBox: typeof import('./../components/GiSplitPane/components/GiSplitPaneFlexibleBox.vue')['default']
    GiSvgIcon: typeof import('./../components/GiSvgIcon/index.vue')['default']
    GiTable: typeof import('./../components/GiTable/src/GiTable.vue')['default']
    GiTag: typeof import('./../components/GiTag/index.tsx')['default']
    GiThemeBtn: typeof import('./../components/GiThemeBtn/index.vue')['default']
    HourForm: typeof import('./../components/GenCron/CronForm/component/hour-form.vue')['default']
    Icon403: typeof import('./../components/icons/Icon403.vue')['default']
    Icon404: typeof import('./../components/icons/Icon404.vue')['default']
    Icon500: typeof import('./../components/icons/Icon500.vue')['default']
    IconBorders: typeof import('./../components/icons/IconBorders.vue')['default']
    IconTableSize: typeof import('./../components/icons/IconTableSize.vue')['default']
    IconTreeAdd: typeof import('./../components/icons/IconTreeAdd.vue')['default']
    IconTreeReduce: typeof import('./../components/icons/IconTreeReduce.vue')['default']
    JsonPretty: typeof import('./../components/JsonPretty/index.vue')['default']
    LineChart: typeof import('./../components/echartCom/lineChart/lineChart.vue')['default']
    MergedInfiniteTable: typeof import('./../components/MergedInfiniteTable/MergedInfiniteTable.vue')['default']
    MinuteForm: typeof import('./../components/GenCron/CronForm/component/minute-form.vue')['default']
    MonthForm: typeof import('./../components/GenCron/CronForm/component/month-form.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    PieChart: typeof import('./../components/echartCom/pieChart/pieChart.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecondForm: typeof import('./../components/GenCron/CronForm/component/second-form.vue')['default']
    SplitPanel: typeof import('./../components/SplitPanel/index.vue')['default']
    TextCopy: typeof import('./../components/TextCopy/index.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    Verify: typeof import('./../components/Verify/index.vue')['default']
    VerifyPoints: typeof import('./../components/Verify/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../components/Verify/Verify/VerifySlide.vue')['default']
    WeekForm: typeof import('./../components/GenCron/CronForm/component/week-form.vue')['default']
    YearForm: typeof import('./../components/GenCron/CronForm/component/year-form.vue')['default']
  }
}
