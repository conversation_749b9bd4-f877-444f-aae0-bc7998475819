// 响应式设计工具混入

// CSS 断点常量
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  SMALL_MOBILE: 480,
  LARGE_DESKTOP: 1200
}

// 媒体查询字符串
export const MEDIA_QUERIES = {
  mobile: `(max-width: ${BREAKPOINTS.MOBILE - 1}px)`,
  tablet: `(min-width: ${BREAKPOINTS.MOBILE}px) and (max-width: ${BREAKPOINTS.TABLET - 1}px)`,
  desktop: `(min-width: ${BREAKPOINTS.TABLET}px)`,
  smallMobile: `(max-width: ${BREAKPOINTS.SMALL_MOBILE - 1}px)`,
  largeMobile: `(min-width: ${BREAKPOINTS.SMALL_MOBILE}px) and (max-width: ${BREAKPOINTS.MOBILE - 1}px)`,
  largeDesktop: `(min-width: ${BREAKPOINTS.LARGE_DESKTOP}px)`
}

import { ref, computed, onMounted, onBeforeUnmount } from 'vue'

export function useResponsive() {
  const screenWidth = ref(window.innerWidth)
  const resizeTimer = ref<number | null>(null)

  const breakpoints = computed(() => ({
    mobile: screenWidth.value < 768,
    tablet: screenWidth.value >= 768 && screenWidth.value < 1024,
    desktop: screenWidth.value >= 1024,
    smallMobile: screenWidth.value < 480,
    largeMobile: screenWidth.value >= 480 && screenWidth.value < 768,
    smallTablet: screenWidth.value >= 768 && screenWidth.value < 900,
    largeTablet: screenWidth.value >= 900 && screenWidth.value < 1024,
    smallDesktop: screenWidth.value >= 1024 && screenWidth.value < 1200,
    largeDesktop: screenWidth.value >= 1200,
  }))

  const deviceType = computed(() => {
    if (breakpoints.value.mobile) return 'mobile'
    if (breakpoints.value.tablet) return 'tablet'
    return 'desktop'
  })

  function updateScreenSize() {
    screenWidth.value = window.innerWidth
  }

  function handleResize() {
    if (resizeTimer.value) clearTimeout(resizeTimer.value)
    resizeTimer.value = window.setTimeout(() => {
      updateScreenSize()
    }, 100)
  }

  function getResponsiveValue<T>(mobile: T, tablet?: T, desktop?: T): T {
    if (breakpoints.value.mobile) return mobile
    if (breakpoints.value.tablet) return tablet ?? mobile
    return desktop ?? tablet ?? mobile
  }

  function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }

  function getViewportHeight() {
    return window.innerHeight || document.documentElement.clientHeight
  }

  function scrollToElement(element: string | HTMLElement, offset = 0) {
    if (typeof element === 'string') {
      element = document.querySelector(element) as HTMLElement
    }
    if (element) {
      const top = element.offsetTop - offset
      window.scrollTo({ top, behavior: 'smooth' })
    }
  }

  onMounted(() => {
    updateScreenSize()
    window.addEventListener('resize', handleResize)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    screenWidth,
    breakpoints,
    deviceType,
    getResponsiveValue,
    isTouchDevice,
    getViewportHeight,
    scrollToElement,
  }
}
