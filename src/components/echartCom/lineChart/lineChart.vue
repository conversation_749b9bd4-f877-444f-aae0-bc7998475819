<script setup lang="ts">
import { computed, defineProps } from 'vue'
import VChart from 'vue-echarts'

const props = defineProps<{
    categories: string[]
    seriesData: number[]
    background?: string
    smooth?: boolean
    styleProps?: Record<string, any>
}>()

const option = computed(() => ({
    backgroundColor: props.background || '',
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'line',
            lineStyle: {
                type: 'dashed',
                color: '#aaa'
            }
        }
    },
    xAxis: {
        type: 'category',
        data: props.categories
    },

    yAxis: {
        type: 'value'
    },
    series: [
        {
        data: props.seriesData,
        type: 'line',
        smooth: props.smooth || false
        }
    ]
}))
</script>

<template>
   <v-chart :option="option" :style="styleProps" autoresize/>
</template>