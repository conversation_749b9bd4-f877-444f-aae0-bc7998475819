

<template>
  <v-chart
    :option="option"
    :theme="theme"
    :autoresize="true"
    :style="styleProps"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/useAppStore'

interface Props {
  seriesData: { value: number; name: string }[]
  background?: string
  styleProps?: Record<string, any>
}

const props = defineProps<Props>()
const appStore = useAppStore()
const theme = computed(() => appStore.theme)

const option = computed(() => ({
  backgroundColor: props.background ?? (theme.value === 'dark' ? '#000' : '#fff'),
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'horizontal',
    bottom: '0',
    left: 'center'
  },
  series: [
    {
      name: '来源',
      type: 'pie',
      radius: '50%',
      data: props.seriesData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))
</script>