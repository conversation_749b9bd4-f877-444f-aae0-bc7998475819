<template>
  <v-chart
    :option="chartOption"
    autoresize
    :theme="theme"
    style="width: 100%; height: 400px"
  />
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import { useAppStore } from '@/stores/useAppStore'
import type { EChartsOption, BarSeriesOption } from 'echarts'
import type { PropType } from 'vue'

// props
const props = defineProps({
  categories: {
    type: Array as PropType<string[]>,
    required: true
  },
  seriesData: {
    type: Array as PropType<Array<{ name: string; data: number[] }>>,
    required: true
  },
  labelConfig: {
    type: Object as PropType<Partial<BarLabelOption>>,
    default: () => ({})
  },
  showToolbox: {
    type: Boolean,
    default: true
  },
  chartType: {
    type: String as PropType<'bar' | 'stack' | 'line'>,
    default: 'bar'
  },
  seriesStyle: {
    type: Object as PropType<Partial<BarSeriesOption>>,
    default: () => ({})
  }
})

const appStore = useAppStore()
const theme = computed(() => appStore.theme)
// 类型
type BarLabelOption = NonNullable<BarSeriesOption['label']>

const chartOption = computed<EChartsOption>(() => {
  const baseLabel: BarLabelOption = {
    show: false,
    position: 'insideBottom',
    distance: 15,
    align: 'left',
    verticalAlign: 'middle',
    rotate: 90,
    formatter: '{c}  {name|{a}}',
    fontSize: 10,
    rich: {
      name: {}
    },
    ...props.labelConfig
  }

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    legend: {
      top: 'bottom',
      left: 'center',
      data: props.seriesData.map((s) => s.name)
    },
    toolbox: props.showToolbox
      ? {
          show: true,
          orient: 'vertical',
          left: 'right',
          top: 'center',
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: {
              show: true,
              type: ['line', 'bar', 'stack']
            },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        }
      : undefined,
    xAxis: [
      {
        type: 'category',
        axisTick: { show: false },
        data: props.categories
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    backgroundColor: theme.value === 'dark' ? '#000' : '#fff',
    series: props.seriesData.map((s) => ({
      name: s.name,
      type: props.chartType === 'stack' ? 'bar' : props.chartType,
      stack: props.chartType === 'stack' ? 'total' : undefined,
      barGap: 0,
      label: baseLabel,
      emphasis: { focus: 'series' },
      data: s.data,...props.seriesStyle
    }))
  }
})
</script>

<style scoped>
</style>