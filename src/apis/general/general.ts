import http from '@/utils/http';


// 通用接口列表
// 行业
export function getIndustryApi() {
  return http.get('/business/industry/list');
}
// 客户列表
export function getCustomerApi() {
  return http.get('/business/customer/list');
}
// 产品列表
export function getProductApi(params: Record<string, any>) {
  return http.get(`/business/product/list`,params);
}
// 当前账户下可管理的运营列表
export function getManageOperaListApi() {
  return http.get(`/system/user/current/manageableUsers`);
}
// 条件查询下账户下可管理的运营列表
// export function getAdAccountManageOperaListApi(id: number) {
//   return http.get(`/business/customer/user/${id}`);
// }

