import http from '@/utils/http';

// 账户
// 列表
export function getACReportListApi(params: Record<string, any>) {
  return http.get('/report/media/account/metrics',  params  );
}

export function getProductListApi(params: Record<string, any>) {
  return http.get('/business/product/list',  params  );
}

// 导出
// 导出 Excel
export function exportACReportListApi(params: Record<string, any>) {
  return http.download('/report/media/account/metrics/excel', params);
}
