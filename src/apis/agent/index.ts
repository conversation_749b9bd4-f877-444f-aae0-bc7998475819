import http from '@/utils/http';

// 百度
// 卡片接口
export function getBDCardDataApi() {
  return http.post('/media/wenluan/performance');
}
// 新客列表
export function getNewReportApi(type: number) {
  return http.get(`/media/wenluan/reports/${type}`);
}
// 消耗top10 & 重点框架监控
export function getDepleteApi(type: number) {
  return http.get(`/media/wenluan/ecommerce/${type}`);
}
// 新客激励
export function getNewMotivateApi() {
  return http.get('/media/wenluan/incentive');
}
// 新客体量
export function getNewVolumeApi() {
  return http.get('/media/wenluan/volume');
}
// 业绩报表
export function getPerformanceListApi(params: Record<string, any>) {
  return http.post('/media/wenluan/yjdetail',params);
}

// 账户报表
export function getAccountListApi(params: Record<string, any>) {
  return http.post('/media/wenluan/accountdetail',params);
}

// 框架报表
export function getFrameworkListApi(params: Record<string, any>) {
  return http.post('/media/wenluan/frameworkdetail',params);
}

// 一级行业
export function getIndustry1rApi() {
  return http.get('/media/wenluan/industry');
}

// 业绩-汇总数据-导出
export function collectExportListApi(params: Record<string, any>) {
  return http.postDownload('/media/wenluan/PerformanceDetailStatisticsDataDownloadAd', params);
}

// 业绩导出
export function exportListApi(params: Record<string, any>) {
  return http.postDownload('/media/wenluan/DataDownloadYj', params);
}
// 框架导出
export function frameExportListApi(params: Record<string, any>) {
  return http.postDownload('/media/wenluan/DataDownloadKj', params)
}
// 账户导出
export function accountExportListApi(params: Record<string, any>) {
  return http.postDownload('/media/wenluan/DataDownloadAd', params)
}
// 业绩明细
export function performanceDetailApi(params: Record<string, any>) {
  return http.post('/media/wenluan/PerformanceDetailStatistics', params)
}
