import http from '@/utils/http';

// 媒体账户
// 列表
export function getBusinessInfo(params: Record<string, any>) {
  return http.post('/media/adAccount/businessInfo', params);
}
// 记录
export function getRecordList(id: string, params: Record<string, any>) {
  return http.get(`/media/adAccount/${id}/owner`, params);
}
// 记录
export function deleteRecordApi(id: string) {
  return http.del(`/media/adAccount/${id}/owner`);
}
// 新增-授权
export function sendAuthorized(params: Record<string, any>) {
  return http.get('/auth/media/ad/link', params);
}
// 查询用户是否授权
export function checkAuthApi(uid: string) {
  return http.get(`/auth/media/ad/status/${uid}`);
}
// 批量修改
export function batchUpdateApi(params: Record<string, any>) {
  return http.post('/media/adAccount/owner/batchBind', params);
}
// 导出
export function exportApi(params: Record<string, any>) {
  return http.postDownload('/media/adAccount/businessInfo/report', params);
}




// 客户
// 列表
export function getCustomerApi(params: Record<string, any>) {
  return http.get('/business/customer', params);
}
// 客户明细
export function checkDetailApi(id: string, params: Record<string, any>) {
  return http.get(`/business/customer/detail/${id}`,params);
}


// 产品
// 列表
export function getProductApi(params: Record<string, any>) {
  return http.get('/business/product', params);
}
// 新增
export function addProductApi(params: Record<string, any>) {
  return http.post(`/business/product`,params);
}

// async function getBusinessInfo(params) {
//   // 修改请求方式为POST，将params作为请求体传入
//   const response = await http.post('/api/business/info', params);
//   return response.data;
// }
