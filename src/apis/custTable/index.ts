import http from '@/utils/http';

// 新客配置
// 新客开发
export function getCustDevelopApi(type:number) {
  return http.get(`/media/wenluan/customer/${type}`);
}
// 新增
export function addAnyCustmerApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/addcustomer',parm);
}
// 删除
export function deleteAnyCustmerApi(type:number) {
  return http.del(`/media/wenluan/deletecustomer/${type}`);
}
// 编辑
export function editAnyCustmerApi(parm: Record<string, any>) {
  return http.post(`/media/wenluan/updatecustomer`,parm);
};

// 非共服客户
// 列表
export function getNotShareApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/nonunion',parm);
}
// 新增
export function addNotShareApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/addnonunion',parm);
}
// 编辑
export function editNotShareApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/updatenonunion',parm);
}
// 删除
export function deleteNotShareApi(id: string) {
  return http.del(`/media/wenluan/deletenonunion/${id}`);
}

// 重点框架配置
// 列表
export function getStressFrameApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/framecustomer',parm);
}
// 新增
export function addStressFrameApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/addframecustomer',parm);
}
// 编辑
export function editStressFrameApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/updateframecustomer',parm);
}
// 删除
export function deletetStressFrameApi(id: string) {
  return http.del(`/media/wenluan/deleteframecustomer/${id}`);
}

// 百度文鸾配置表
// 提交
export function submitPerforConfigApi(parm: Record<string, any>) {
  return http.post('/media/wenluan/updateTaskNum',parm);
}
// 获取
export function getPerforConfigApi() {
  return http.post('/media/wenluan/queryTaskNum');
}


