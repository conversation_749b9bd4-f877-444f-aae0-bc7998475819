// src/stores/generalData.ts
import { defineStore } from 'pinia'
import { getIndustryApi, getCustomerApi, getManageOperaListApi } from '@/apis/general/general'
import { getBDCardDataApi } from '@/apis/agent'

export const useGeneralDataStore = defineStore('generalData', {
  state: () => ({
    customerList: [] as { id: string | number, name: string | undefined, businessLine: string | undefined, saleName: string | undefined }[],
    industryList: [] as { id: string | number, name: string }[],
    manageOperaList: [] as { id: string | number, name: string }[],
    mediaList: [
      { id: 1, name: '磁力引擎', visible: true },
      { id: 2, name: '巨量千川', visible: true },
      { id: 3, name: '巨量广告', visible: true },
      { id: 4, name: '百度广告', visible: true },
      { id: 5, name: '京准通', visible: true },
      { id: 5, name: '广点通', visible: true }
    ] as { id: number, name: string, visible: boolean }[],
    infoList: [
      { id: 1, name: '客户', visible: true },
      { id: 2, name: '产品', visible: true },
      { id: 3, name: '运营', visible: true },
    ],
    bdCardData: null as any,
  }),
  actions: {
    async fetchCustomerList() {
      const res = await getCustomerApi()
      this.customerList = res.data.map(item => ({
        id: item.id,
        name: item.contractEntityName,
        businessLine: item.businessLine,
        saleName: item.saleName
      }))
      console.log(res, '客户列表')
    },
    async fetchIndustryList() {
      const res = await getIndustryApi()
      this.industryList = res.data.map(item => ({
        id: item.id,
        name: item.industryName
      }))
    },
    async getManageOperaList() {
      const res = await getManageOperaListApi()

      this.manageOperaList = res.data.map(item => ({
        id: item.id,
        name: item.nickname
      }))
      console.log(this.manageOperaList, 'manageOperaList')
    },
    /**
     * 媒体列表查询
     * 在引用页面使用方式 ⬇️
       mediaOptions：是最终渲染的数组
       import { useGeneralDataStore } from '@/stores/generalData'
       const generalStore = useGeneralDataStore()
       const mediaOptions = computed(() =>
        generalStore.mediaList.filter((m) => m.visible),
       );
       作用：可隐藏某一条媒体
       generalStore.toggleMediaVisibility(1, false);
     */
    toggleMediaVisibility(id: number, visible: boolean) {
      const item = this.mediaList.find(m => m.id === id);
      if (item) {
        item.visible = visible;
      }
    },
    // 百度卡片数据
    // async fetchBDCardData() {
    //   const res = await getBDCardDataApi()
    //   this.bdCardData = res.data
    // },
    async init() {
      await Promise.all([
        this.fetchCustomerList(),
        this.fetchIndustryList(),
        this.getManageOperaList(),
        // this.fetchBDCardData()
      ])
    }
  },
  persist: false // 需要本地持久化时加上
})
