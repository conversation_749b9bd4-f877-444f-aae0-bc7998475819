// src/stores/useAppStore.ts
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    theme: 'macarons' as 'macarons' | 'dark',
    token: '',
    username: '',
    avatar: '',
    isExpand: JSON.parse(localStorage.getItem('isExpand') ?? 'true') as boolean
  }),
  actions: {
    setTheme(val: 'macarons' | 'dark') {
      this.theme = val
      localStorage.setItem('theme-mode', val)
    },
    initTheme() {
      const saved = localStorage.getItem('theme-mode') as 'macarons' | 'dark' | null
      if (saved === 'macarons' || saved === 'dark') {
        this.theme = saved
      } else {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        this.theme = prefersDark ? 'dark' : 'macarons'
      }
    },
    setUserInfo({ token, username, avatar }: { token: string, username: string, avatar: string }) {
      this.token = token
      this.username = username
      this.avatar = avatar
      localStorage.setItem('user-token', token)
      localStorage.setItem('user-name', username)
      localStorage.setItem('user-avatar', avatar)
    },
    initUserInfo() {
      this.token = localStorage.getItem('user-token') || ''
      this.username = localStorage.getItem('user-name') || ''
      this.avatar = localStorage.getItem('user-avatar') || ''
    },
    logout() {
      this.token = ''
      this.username = ''
      this.avatar = ''
      localStorage.removeItem('user-token')
      localStorage.removeItem('user-name')
      localStorage.removeItem('user-avatar')
    },
  }
})