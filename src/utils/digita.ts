import { } from 'vue'

export function animateNumber(
  el: HTMLElement,
  start: number,
  end: number,
  duration: number = 1000
) {
  const startTime = performance.now()
  function tick(now: number) {
    const elapsed = now - startTime
    const progress = Math.min(elapsed / duration, 1)
    const current = Math.floor(start + (end - start) * progress)
    el.innerText = '¥' + current.toLocaleString()
    if (progress < 1) {
      requestAnimationFrame(tick)
    }
  }
  requestAnimationFrame(tick)
}

export const animateNumberDirective = {
  mounted(el: HTMLElement, binding: any) {
    const target = binding.value
    const duration = 1000
    const start = 0
    const startTime = performance.now()

    function tick(now: number) {
      const elapsed = now - startTime
      const progress = Math.min(elapsed / duration, 1)
      const current = Math.floor(start + (target - start) * progress)
      el.innerText = '¥' + current.toLocaleString()
      if (progress < 1) {
        requestAnimationFrame(tick)
      }
    }

    requestAnimationFrame(tick)
  },
  updated(el: HTMLElement, binding: any) {
    if (binding.value !== binding.oldValue) {
      const target = binding.value
      const duration = 1000
      const start = Number(el.innerText) || 0
      const startTime = performance.now()

      function tick(now: number) {
        const elapsed = now - startTime
        const progress = Math.min(elapsed / duration, 1)
        const current = Math.floor(start + (target - start) * progress)
        el.innerText = '¥' + current.toLocaleString()
        if (progress < 1) {
          requestAnimationFrame(tick)
        }
      }

      requestAnimationFrame(tick)
    }
  }
}