// src/utils/request.ts
import axios from 'axios'

// 创建实例
const http = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000,
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    const { code, data, msg } = response.data
    if (code === 200) {
      return data
    } else {
      window.$message?.error?.(msg || '请求失败')
      return Promise.reject(msg)
    }
  },
  error => {
    window.$message?.error?.(error.message || '网络错误')
    return Promise.reject(error)
  }
)

export default http