type ImportVueFileType = typeof import('*.vue')
type ImportVueFileFnType = () => Promise<ImportVueFileType>

const moduleFiles = import.meta.glob<ImportVueFileType>('@/views/**/*.vue')

export const asyncRouteModules = Object.entries(moduleFiles).reduce((routes, [url, importFn]) => {
  if (!/\/(views\/login|components)\//.test(url)) {
    const path = url.replace('/src/views/', '').replace('.vue', '')
    routes[path] = importFn
  }

  return routes
}, {} as Recordable<ImportVueFileFnType>)

import article from './modules/article'
import Agency from './modules/business'
import Baidu from './modules/baidu'
import JDexpress from './modules/JDexpress'
import dataView from './modules/dataView'
import dataReport from './modules/dataReport'
import custTable from './modules/custTable'
import departBoard from './modules/departBoard'
import individualBoard from './modules/individualBoard'

export const asyncRoutes = [
  article,
  Agency,
  Baidu,
  JDexpress,
  dataView,
  dataReport,
  custTable,
  departBoard,
  individualBoard,
  // 其他模块
]
