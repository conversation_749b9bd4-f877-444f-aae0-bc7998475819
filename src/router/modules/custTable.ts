const Layout = () => import('@/layout/index.vue')

export default {
  path: '/custTable',
  name: 'CustTable',
  component: Layout,
  meta: {
    title: '自定义报表配置',
    icon: 'document', // 可改为你想要的图标
  },
  children: [
    {
      path: 'list',
      name: 'newCust',
      component: () => import('@/views/custTable/newCust/index.vue'),
      meta: {
        title: '新客配置',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },

    },
    {
      path: 'list',
      name: 'perforConfig',
      component: () => import('@/views/custTable/perforConfig/index.vue'),
      meta: {
        title: '百度文鸾配置表',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },

    }
  ],

}