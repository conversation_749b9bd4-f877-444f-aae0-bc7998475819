const Layout = () => import('@/layout/index.vue')

export default {
  path: '/JDexpress',
  name: 'JDexpress',
  component: Layout,
  meta: {
    title: '代运营业务',
    icon: 'document', // 可改为你想要的图标
  },
  children: [
    {
      path: 'list',
      name: 'project-manage',
      component: () => import('@/views/business/JDexpress/index.vue'),
      meta: {
        title: '京淮通',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },
    }
  ],
}