const Layout = () => import('@/layout/index.vue')

export default {
  path: '/article',
  name: 'Article',
  component: Layout,
  meta: {
    title: '文章管理',
    icon: 'document', // 可改为你想要的图标
  },
  children: [
    {
      path: 'list',
      name: 'ArticleList',
      component: () => import('@/views/article/list/index.vue'),
      meta: {
        title: '文章列表',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },
    },
    {
        path: 'list/detail',
        name: 'ArticleDetail',
        component: () => import('@/views/article/list/detail.vue'),
        meta: {
            title: '编辑文章',
            icon: 'detail',
            hidden: true // 如果你不希望左侧菜单显示这个页面
        }
    }
  ],
}