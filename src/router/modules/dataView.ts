const Layout = () => import('@/layout/index.vue')

export default {
  path: '/dataView',
  name: 'DataView',
  component: Layout,
  meta: {
    title: '数据看板',
    icon: 'document', // 可改为你想要的图标
  },
  children: [
    {
      path: 'list',
      name: 'data',
      component: () => import('@/views/dataView/areaBoard/index.vue'),
      meta: {
        title: '数据看板',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },
    }
  ],
}