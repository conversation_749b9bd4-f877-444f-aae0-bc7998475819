const Layout = () => import('@/layout/index.vue')

export default {
  path: '/departBoard',
  name: 'DepartBoard',
  component: Layout,
  meta: {
    title: '部门看板',
    icon: 'document', // 可改为你想要的图标
  },
  children: [
    {
      path: 'list',
      name: 'data',
      component: () => import('@/views/dataView/departBoard/index.vue'),
      meta: {
        title: '部门看板',
        icon: 'list',
        keepAlive: true,
        roles: ['admin', 'editor'], // 角色权限控制
      },
    }
  ],
}