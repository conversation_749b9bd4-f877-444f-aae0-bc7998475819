# 环境变量 (命名必须以 VITE_ 开头)
# 接口前缀
VITE_API_PREFIX = '/api'

# 接口地址
# VITE_API_BASE_URL = 'http://336379f0.r21.cpolar.top'
# VITE_API_BASE_URL = 'http://192.168.3.151:8000'
VITE_API_BASE_URL = 'http://31369011irbs.vicp.fun'
# VITE_API_BASE_URL = 'https://seenow-api.jcengine.com/api/'


# 接口地址 (WebSocket)
VITE_API_WS_URL = 'wss://seenow-api.jcengine.com/api'
# 地址前缀
VITE_BASE = '/'

# 是否开启开发者工具
VITE_OPEN_DEVTOOLS = false

# 应用配置面板
VITE_APP_SETTING = true

# 客户端ID
VITE_CLIENT_ID = 'ef51c9a3e9046c4f2ea45142c8a8344a'